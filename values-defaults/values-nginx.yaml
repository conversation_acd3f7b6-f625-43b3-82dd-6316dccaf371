# yaml-language-server: https://raw.githubusercontent.com/bitnami/charts/refs/tags/nginx/18.2.4/bitnami/nginx/values.schema.json
# https://github.com/bitnami/charts/blob/nginx/18.2.4/bitnami/nginx/values.yaml
metrics:
  enabled: true

tls:
  existingSecret: infra-tls-secret

# we don't have an index.html file, so we have to disable the readinessProbe
readinessProbe:
  enabled: false

serverBlock: |-
  server {
    server_name  _;
    listen 0.0.0.0:8080;

    # Allow access to .jpg files
    location ~ \.(jpg)$ {
      root /app;
    }

    # Allow access to /status endpoint
    location /status {
      stub_status on;
      access_log   off;
      allow 127.0.0.1;
      deny all;
    }

    # Deny access to all other files
    location / {
      deny all;
    }
  }

service:
  type: ClusterIP

# this references the directory with the images
staticSitePVC: nginx-pvc

# this will expose port 80 and 443 on the host which is needed
#  for the legacy dashboard to load the images from
hostNetwork: true

containerPorts:
  http: 80
  https: 443

# required to use port 80 and 443 as they are privileged ports
podSecurityContext:
  runAsUser: 0
  runAsGroup: 0

containerSecurityContext:
  enabled: false

extraDeploy:
  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: nginx-pvc
    spec:
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: 1Gi
      storageClassName: ""
      volumeName: nginx-pv
  - kind: PersistentVolume
    apiVersion: v1
    metadata:
      name: nginx-pv
    spec:
      capacity:
        storage: 1Gi
      accessModes:
        - ReadWriteOnce
      storageClassName: ""
      volumeMode: Filesystem
      persistentVolumeReclaimPolicy: Retain
      hostPath:
        path: /media/fingermark/storage/nginx-infra
