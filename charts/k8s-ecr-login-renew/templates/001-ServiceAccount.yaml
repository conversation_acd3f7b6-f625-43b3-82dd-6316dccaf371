apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ required "A service account name is required" .Values.names.serviceAccount }}
  namespace: {{ .Release.Namespace | default "default" }}
{{- if .Values.forHelm }}
  labels:
    app.kubernetes.io/name: {{ .Chart.Name }}
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}
