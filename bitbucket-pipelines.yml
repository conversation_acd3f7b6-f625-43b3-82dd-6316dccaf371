image: alpine/helm:3

pipelines:
  default:
    - step:
        name: <PERSON><PERSON>
        script:
          - apk add --no-cache make
          - helm plugin install https://github.com/hypnoglow/helm-s3.git
          - helm plugin install https://github.com/helm-unittest/helm-unittest.git
          - make replace-proxy-with-s3
          - make helm-dependency-update-all
          - make helm-lint-all
          - make helm-test
