---
# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: Deployment Test Suite
templates:
  - classes/deployment.yaml
tests:
  - it: Should create an empty deployment
    set:
      name: testing
      containers: []
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: metadata.name
          value: testing
      - notExists:
          path: spec.template.spec.containers

  - it: Should create a deployment with a single container
    set:
      name: testing
      annotations:
        key1: value1
      containers:
        - name: container1
          image:
            repository: nginx
            tag: latest
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: metadata
          value:
            name: testing
            namespace: NAMESPACE
            annotations:
              key1: value1
            labels:
              app.kubernetes.io/instance: RELEASE-NAME
              app.kubernetes.io/managed-by: Helm
              app.kubernetes.io/name: testing
              helm.sh/chart: eyecue-common-test-1.0.0
      - equal:
          path: spec
          value:
            replicas: 1
            selector:
              matchLabels:
                app.kubernetes.io/instance: RELEASE-NAME
                app.kubernetes.io/name: testing
            template:
              metadata:
                labels:
                  app.kubernetes.io/instance: RELEASE-NAME
                  app.kubernetes.io/managed-by: Helm
                  app.kubernetes.io/name: testing
                  helm.sh/chart: eyecue-common-test-1.0.0
                annotations:
                  key1: value1
              spec:
                containers:
                  - name: container1
                    image: nginx:latest
                    imagePullPolicy: IfNotPresent
                    resources:
                      limits:
                        cpu: "1"
                        memory: 1Gi
                      requests:
                        cpu: 500m
                        memory: 500Mi

  - it: Should create a deployment env and envFrom
    set:
      name: testing
      containers:
        - name: container1
          image:
            repository: nginx
            tag: latest
          env:
            - name: ENV1
              value: VALUE1
            - name: ENV2
              value: VALUE2
          envFrom:
            - configMapRef:
                name: configmap1
            - secretRef:
                name: secret1
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: spec.template.spec.containers[0].env
          value:
            - name: ENV1
              value: VALUE1
            - name: ENV2
              value: VALUE2
      - equal:
          path: spec.template.spec.containers[0].envFrom
          value:
            - configMapRef:
                name: configmap1
            - secretRef:
                name: secret1

  - it: Should create a deployment with a volume and volumeMount
    set:
      name: testing
      config:
        volumeName: volume1
      volumes:
        - name: "{{ .Values.config.volumeName }}"
          emptyDir: {}
      containers:
        - name: container1
          image:
            repository: nginx
            tag: latest
          volumeMounts:
            - name: "{{ .Values.config.volumeName }}"
              mountPath: /data

    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: spec.template.spec.containers[0].volumeMounts
          value:
            - name: volume1
              mountPath: /data
      - equal:
          path: spec.template.spec.volumes
          value:
            - name: volume1
              emptyDir: {}

  - it: Should template the command and args
    set:
      name: testing
      config:
        key1: value1
        key2: value2
      containers:
        - name: container1
          image:
            repository: nginx
            tag: latest
          command:
            - "echo"
            - "hello {{ .Values.config.key1 }}"
          args:
            - "world {{ .Values.config.key2 }}"
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: spec.template.spec.containers[0].command
          value:
            - "echo"
            - "hello value1"
      - equal:
          path: spec.template.spec.containers[0].args
          value:
            - "world value2"

  - it: Should create a deployment with an init container
    set:
      name: testing
      containers:
        - name: container1
          image:
            repository: nginx
            tag: latest
      initContainers:
        - name: initContainer1
          image:
            repository: busybox
            tag: latest
          command:
            - "echo"
            - "hello"
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: spec.template.spec
          value:
            initContainers:
              - name: initContainer1
                image: busybox:latest
                imagePullPolicy: IfNotPresent
                command:
                  - "echo"
                  - "hello"
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: 500m
                    memory: 500Mi
            containers:
              - name: container1
                image: nginx:latest
                imagePullPolicy: IfNotPresent
                resources:
                  limits:
                    cpu: "1"
                    memory: 1Gi
                  requests:
                    cpu: 500m
                    memory: 500Mi
