{"type": "object", "additionalProperties": false, "definitions": {"containerSpec": {"type": "object", "properties": {"name": {"type": "string"}, "image": {"type": "object", "properties": {"repository": {"type": "string"}, "tag": {"type": "string"}}, "required": ["repository", "tag"]}, "imagePullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"], "default": "IfNotPresent"}, "command": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/command"}, "args": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/args"}, "env": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/env"}, "envFrom": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/envFrom"}, "ports": {"type": "array", "items": {"type": "object", "properties": {"containerPort": {"type": "integer"}, "protocol": {"type": "string", "enum": ["TCP", "UDP"], "default": "TCP"}, "name": {"type": "string"}}, "required": ["containerPort"]}}, "resources": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/resources"}, "volumeMounts": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.Container/properties/volumeMounts"}}, "required": ["name", "image"]}}, "properties": {"type": "object", "properties": {"name": {"type": "string"}, "namespace": {"type": "string"}, "labels": {"$ref": "#/definitions/labels"}, "annotations": {"$ref": "#/definitions/annotations"}, "volumes": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.PodSpec/properties/volumes"}, "imagePullSecrets": {"$ref": "https://raw.githubusercontent.com/kubernetes/kubernetes/refs/tags/v1.31.0/api/openapi-spec/v3/apis__apps__v1_openapi.json#/components/schemas/io.k8s.api.core.v1.PodSpec/properties/imagePullSecrets"}, "initContainers": {"type": "array", "items": {"$ref": "#/definitions/containerSpec"}}, "containers": {"type": "array", "items": {"$ref": "#/definitions/containerSpec"}}}, "required": ["name"]}}