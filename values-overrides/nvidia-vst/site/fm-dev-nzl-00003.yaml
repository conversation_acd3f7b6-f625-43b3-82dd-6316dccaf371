configs:
  rtsp_streams.json:
    streams:
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.164/MediaInput/stream_2
        name: camera001
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.165/MediaInput/stream_2
        name: camera002
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.154/MediaInput/stream_2
        name: camera010
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.150/MediaInput/stream_2
        name: camera11
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.151/MediaInput/stream_2
        name: camera12
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.166/MediaInput/stream_2
        name: camera13
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.153/MediaInput/stream_2
        name: camera14
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.160/MediaInput/stream_2
        name: camera15
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.155/MediaInput/stream_2
        name: camera16
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.168/MediaInput/stream_2
        name: camera027
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.172/MediaInput/stream_2
        name: camera031
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.152/MediaInput/stream_2
        name: camera036
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.156/MediaInput/stream_2
        name: camera156
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.157/MediaInput/stream_2
        name: camera157
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.158/MediaInput/stream_2
        name: camera158
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.159/MediaInput/stream_2
        name: camera159
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.161/MediaInput/stream_2
        name: camera161
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.162/MediaInput/stream_2
        name: camera162
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.163/MediaInput/stream_2
        name: camera163
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.167/MediaInput/stream_2
        name: camera167
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.169/MediaInput/stream_2
        name: camera169
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.170/MediaInput/stream_2
        name: camera170
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.171/MediaInput/stream_2
        name: camera171
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.173/MediaInput/stream_2
        name: camera173
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.174/MediaInput/stream_2
        name: camera174
      - enabled: false
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.175/MediaInput/stream_2
        name: camera175
      - enabled: true
        stream_in: rtsp://admin:Q1w2e3r4@10.25.48.176/profile2/media.smp
        name: camera176
