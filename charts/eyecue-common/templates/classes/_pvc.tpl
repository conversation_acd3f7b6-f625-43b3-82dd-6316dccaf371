{{- define "eyecue.common.pvc" -}}
  {{- $rootContext := .rootContext -}}
  {{- $pvcObject := .object -}}
  {{- $labels := $pvcObject.labels | default dict -}}
  {{- $annotations := $pvcObject.annotations | default dict -}}
  {{- if $pvcObject.retain }}
    {{- $annotations = merge
      (dict "helm.sh/resource-policy" "keep")
      $annotations
    -}}
  {{- end -}}
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ $pvcObject.name }}
  namespace: {{ include "eyecue.common.namespace" $rootContext }}
  {{- with $labels }}
  labels:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- with $annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  accessModes:
    - {{ default "ReadWriteOnce" $pvcObject.accessMode | quote }}
  resources:
    requests:
      storage: {{ default "1Gi" $pvcObject.size | quote }}
  storageClassName: {{ default "" $pvcObject.storageClass | quote }}
  {{- if $pvcObject.volumeName }}
  volumeName: {{ tpl $pvcObject.volumeName $rootContext | quote }}
  {{- end }}
{{- end -}}
