groups:
  - name: sidecar-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
      - alert: RabbitMQQueueMessagesReady
        expr: rabbitmq_queue_messages_ready > 50
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "RabbitMQ queued messages are too high"
          description: |
            There's been more than 50 messages queued on RabbitMQ .
            {{ $labels.siteId }} - {{ $labels.queue }} - Current instance: {{ printf "%.2f" $value }}
      - alert: RabbitMQQueueMessagesUnacked
        expr: rabbitmq_queue_messages_unacked > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "RabbitMQ unacked messages are too high for queue {{ $labels.queue }}"
          description: |
            There's been more than 50 messages unached on RabbitMQ. This could be a sign of a slow/crashed consumer.
            {{ $labels.siteId }} - {{ $labels.queue }} - Current instance: {{ printf "%.2f" $value }}
