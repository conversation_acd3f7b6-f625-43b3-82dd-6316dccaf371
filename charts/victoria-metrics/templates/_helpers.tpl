{{- /*
We override this function here as the original one doesn't
allow use to use multiple rewriteUrls in the correct order.

The function is modified from the original so that the $args
variable is a list of strings instead of a map. This allows
us to use multiple rewriteUrls in the correct order.
*/ -}}
{{- define "vmagent.args" -}}
  {{- $args := default list -}}
  {{- $Values := (.helm).Values | default .Values -}}
  {{- if empty $Values.remoteWrite -}}
    {{- fail "Please define at least one remoteWrite" -}}
  {{- end -}}
  {{- $args := append $args (dict "key" "promscrape.config" "value" "/config/scrape/scrape.yml") -}}
  {{- $args := append $args (dict "key" "remoteWrite.tmpDataPath" "value" "/tmpData") -}}
  {{- range $i, $rw := $Values.remoteWrite -}}
    {{- if not $rw.url -}}
      {{- fail (printf "`url` is not set for `remoteWrite` idx %d" $i) -}}
    {{- end -}}
    {{- range $rwKey, $rwValue := $rw -}}
      {{- $key := printf "remoteWrite.%s" $rwKey -}}
      {{- if or (kindIs "slice" $rwValue) (kindIs "map" $rwValue) -}}
        {{- $args = append $args (dict "key" $key "value" (printf "/config/rw/%d-%s.yaml" $i $rwKey)) -}}
      {{- else -}}
        {{ $args = append $args (dict "key" $key "value" $rwValue) -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
  {{/*
  Removed the following as we are not using the license flag
  {{- $args = mergeOverwrite $args (fromYaml (include "vm.license.flag" .)) -}}
  */}}
  {{- range $key, $value := $Values.extraArgs -}}
    {{- $args = append $args (dict "key" $key "value" $value) -}}
  {{- end -}}
  {{- if and $Values.statefulSet.enabled $Values.statefulSet.clusterMode }}
    {{- $args := append $args (dict "key" "promscrape.cluster.membersCount" "value" "value" $Values.replicaCount) -}}
    {{- $args := append $args (dict "key" "promscrape.cluster.replicationFactor" "value" $Values.statefulSet.replicationFactor) -}}
    {{- $args := append $args (dict "key" "promscrape.cluster.memberNum" "value" "$(POD_NAME)") -}}
  {{- end -}}
  {{- toYaml (fromYaml (include "vm.args2" $args)).args -}}
{{- end -}}

{{- /*
Slightly modified version of the original vm.args
template which has been renamed to vm.args2.
*/ -}}
{{- define "vm.args2" -}}
  {{- $args := default list -}}
  {{- range $argObj := . -}}
    {{- $key := $argObj.key -}}
    {{- $value := $argObj.value -}}
    {{- if not $key -}}
      {{- fail "Empty key in command line args is not allowed" -}}
    {{- end -}}
    {{- if kindIs "slice" $value -}}
      {{- range $v := $value -}}
        {{- $args = append $args (include "vm.arg" (dict "key" $key "value" $v)) -}}
      {{- end -}}
    {{- else -}}
      {{- $args = append $args (include "vm.arg" (dict "key" $key "value" $value)) -}}
    {{- end -}}
  {{- end -}}
  {{- toYaml (dict "args" $args) -}}
{{- end -}}
