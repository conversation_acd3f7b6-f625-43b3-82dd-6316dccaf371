---
# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: Service Test Suite
templates:
  - classes/service.yaml
tests:
  - it: Should create a Service
    set:
      name: testing
      ports:
        http:
          port: 80
          targetPort: 80
          protocol: TCP
    asserts:
      - isKind:
          of: Service
      - equal:
          path: metadata
          value:
            name: testing
            labels:
              app.kubernetes.io/instance: RELEASE-NAME
              app.kubernetes.io/managed-by: Helm
              app.kubernetes.io/service: testing
              helm.sh/chart: eyecue-common-test-1.0.0
      - equal:
          path: spec
          value:
            type: ClusterIP
            selector:
              app.kubernetes.io/instance: RELEASE-NAME
              app.kubernetes.io/name: testing
            ports:
              - port: 80
                name: http
                targetPort: 80
                protocol: TCP
