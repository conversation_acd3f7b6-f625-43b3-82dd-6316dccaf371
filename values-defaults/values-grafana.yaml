# https://github.com/grafana/helm-charts/blob/grafana-8.6.2/charts/grafana/values.yaml
---
adminUser: admin
adminPassword: q1w2e3r4

service:
  enabled: true
  type: NodePort
  nodePort: 30003

datasources:
  datasources.yaml:
    apiVersion: 1
    datasources:
      - name: victoriametrics
        type: prometheus
        access: proxy
        url: http://infra-victoria-metrics-server.infra.svc.cluster.local:8428
        isDefault: true
        editable: false
        orgId: 1
        version: 1
        updateIntervalSeconds: 10

sidecar:
  # auto import dashboards from configmaps or
  # secrets with the label `grafana_dashboard: 1`
  dashboards:
    enabled: true

    label: grafana_dashboard
    labelValue: "1"
