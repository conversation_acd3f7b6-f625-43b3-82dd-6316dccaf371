{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 7, "min": 6, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "sum by(tracker_id) (eyecue_tracker_frames_per_second_gauge_framegrabber{})", "instant": false, "interval": "", "legendFormat": "{{tracker_id}}", "refId": "A"}], "title": "Frames per second FrameGrabber", "transformations": [{"id": "renameByRegex", "options": {"regex": ".*-(.*)", "renamePattern": "$1"}}], "type": "gauge"}, {"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 7, "min": 6, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "sum by(tracker_id) (eyecue_tracker_frames_per_second_gauge_detector{})", "instant": false, "interval": "", "legendFormat": "{{tracker_id}}", "refId": "A"}], "title": "Frames per second Detector", "transformations": [{"id": "renameByRegex", "options": {"regex": ".*-(.*)", "renamePattern": "$1"}}], "type": "gauge"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "sum by(tracker_id) (eyecue_tracker_frames_per_second_gauge_framegrabber{})", "instant": false, "interval": "", "legendFormat": "{{tracker_id}}", "refId": "A"}], "title": "Frames per second FrameGrabber", "transformations": [{"id": "renameByRegex", "options": {"regex": ".*-(.*)", "renamePattern": "$1"}}], "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "sum by(tracker_id) (eyecue_tracker_frames_per_second_gauge_detector{})", "instant": false, "interval": "", "legendFormat": "{{tracker_id}}", "refId": "A"}], "title": "Frames per second Detector", "transformations": [{"id": "renameByRegex", "options": {"regex": ".*-(.*)", "renamePattern": "$1"}}], "type": "timeseries"}], "refresh": false, "schemaVersion": 35, "style": "dark", "tags": ["eyecue"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "FPS Trackers", "uid": "aoaUgMZVk", "version": 1, "weekStart": ""}