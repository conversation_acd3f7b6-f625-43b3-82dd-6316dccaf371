{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 9, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Age (in frames) of the asset when entered ROI. ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": ["min", "mean"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "builder", "expr": "eyecue_tracker_car_age_when_entered_roi", "legendFormat": "{{camera_id}} - {{ roi_id }} ", "range": true, "refId": "A"}], "title": "Asset Age when entered ROI", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "This graph shows how long its past between events created inside this ROI in seconds. ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": ["mean", "min"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "asc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "builder", "expr": "eyecue_tracker_time_during_events", "legendFormat": "{{camera_id}} - {{ roi_id }} ", "range": true, "refId": "A"}], "title": "Time during events", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Assets that got filtered out during the creation of the events. These assets do not produce any event and are effectively marked as \"ghost\"", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "builder", "expr": "eyecue_tracker_car_filtered_in_roi_total", "legendFormat": "{{camera_id}} - {{ roi_id }}  - {{ filter_type }}", "range": true, "refId": "A"}], "title": "Car filtered in ROI", "type": "timeseries"}], "refresh": false, "schemaVersion": 38, "style": "dark", "tags": ["eyecue"], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Tracker Rate Limit", "uid": "c07b9358-235d-4c73-a049-1b3d5c0721be", "version": 3, "weekStart": ""}