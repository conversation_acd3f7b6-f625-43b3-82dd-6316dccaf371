{{- define "eyecue.common.secret" -}}
  {{- $rootContext := .rootContext -}}
  {{- $secretObject := .object -}}
  {{- $labels := $secretObject.labels | default dict -}}
  {{- $annotations := $secretObject.annotations | default dict -}}

  {{- $stringData := "" -}}
  {{- with $secretObject.stringData -}}
    {{- $stringData = (toYaml $secretObject.stringData) | trim -}}
  {{- end -}}
---
apiVersion: v1
kind: Secret
{{- with $secretObject.type }}
type: {{ . }}
{{- end }}
metadata:
  name: {{ $secretObject.name }}
  namespace: {{ include "eyecue.common.namespace" $rootContext }}
  {{- with $labels }}
  labels:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- with $annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
{{- with $stringData }}
stringData: {{- tpl $stringData $rootContext | nindent 2 }}
{{- end }}
{{- end -}}
