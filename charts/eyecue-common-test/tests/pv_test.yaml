---
# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: PV Test Suite
templates:
  - classes/pv.yaml
tests:
  - it: Should create a simple pv
    set:
      name: testing
      hostPath:
        path: /data/hostpath
    asserts:
      - isKind:
          of: PersistentVolume
      - equal:
          path: metadata
          value:
            name: testing
            namespace: NAMESPACE
      - equal:
          path: spec
          value:
            capacity:
              storage: 1Gi
            accessModes:
              - ReadWriteOnce
            storageClassName: ""
            volumeMode: Filesystem
            persistentVolumeReclaimPolicy: Retain
            hostPath:
              path: /data/hostpath
              type: ""
