{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 0}, "id": 3, "options": {"displayMode": "basic", "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.2.2", "targets": [{"editorMode": "builder", "expr": "avg_over_time(eyecue_tracker_HVI_DFA_Metric{metric_name=\"duration\"}[24h])", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Average Duration of HVI per Camera (amount of frames)", "transparent": true, "type": "bargauge"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 18, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 11}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "sum by(camera_id) (increase(eyecue_tracker_hvi_events_counter_total[1h]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "HVIs per Hour (Filtered by Camera)", "transparent": true, "type": "timeseries"}, {"description": "How many HVI happens in each camera/roi in the past 24 hours", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1250}, {"color": "red", "value": 1750}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 23}, "id": 1, "options": {"displayMode": "basic", "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.2.2", "targets": [{"editorMode": "builder", "expr": "increase(eyecue_tracker_hvi_events_counter_total[24h])", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Total HVIs in the Past 24h", "transparent": true, "type": "bargauge"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 34}, "id": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "sum(increase(departures_mongodb_received_departure_messages_counter_total[24h]))", "legendFormat": "Total Departure Rate", "range": true, "refId": "A"}, {"editorMode": "builder", "expr": "sum(increase(eyecue_tracker_hvi_events_counter_total[24h]))", "hide": false, "legendFormat": "Total HVI", "range": true, "refId": "B"}], "title": "Departure Rate vs HVI (In the Past 24 Hours)", "transparent": true, "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 42}, "id": 4, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "avg by(camera_id, roi_id) (eyecue_tracker_HVI_DFA_Metric{metric_name=\"angles_mean\"})", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Average Angle Value During HVIs", "transparent": true, "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 42}, "id": 6, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "avg by(camera_id, roi_id) (eyecue_tracker_HVI_DFA_Metric{metric_name=\"angles_std\"})", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Average Angle Standard Deviation During HVIs", "transparent": true, "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 50}, "id": 5, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "avg by(camera_id, roi_id) (eyecue_tracker_HVI_DFA_Metric{metric_name=\"distances_mean\"})", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Average Distance Value During HVIs", "transparent": true, "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 50}, "id": 7, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "expr": "avg by(camera_id, roi_id) (eyecue_tracker_HVI_DFA_Metric{metric_name=\"distances_std\"})", "legendFormat": "{{camera_id}} : {{roi_id}}", "range": true, "refId": "A"}], "title": "Average Distance Standard Deviation During HVIs", "transparent": true, "type": "stat"}], "refresh": "", "schemaVersion": 39, "tags": ["eyecue"], "templating": {"list": [{"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "definition": "query_result(sum(increase(departures_mongodb_received_departure_messages_counter_total[24h])))", "description": "The departure rate value from the last 24h", "hide": 1, "includeAll": false, "multi": false, "name": "departure_rate_last_24h", "options": [], "query": {"query": "query_result(sum(increase(departures_mongodb_received_departure_messages_counter_total[24h])))", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": ".*? (\\d+) .*", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "HVI", "uid": "f876cbcf-89aa-4e16-bc4e-0007a1b8e535", "version": 3, "weekStart": ""}