{"type": "object", "additionalProperties": false, "properties": {"annotations": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "labels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "name": {"type": "string"}, "volumeName": {"type": "string"}, "retain": {"type": "boolean"}, "size": {"type": "string"}, "accessMode": {"type": "string"}, "storageClass": {"type": "string"}}, "required": ["name", "volumeName"]}