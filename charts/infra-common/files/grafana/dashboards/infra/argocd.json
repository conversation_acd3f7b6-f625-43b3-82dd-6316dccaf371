{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Official ArgoCD Dashboard as of June 2021", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 18, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 68, "panels": [], "title": "Overview", "type": "row"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 1}, "id": 26, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<div style=\"text-align: center;\">\n    <img src=\"https://avatars1.githubusercontent.com/u/30269780?s=110&v=4\" alt=\"argoimage\">\n</div>", "mode": "markdown"}, "pluginVersion": "11.3.1", "title": "", "transparent": true, "type": "text"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 2, "y": 1}, "id": 32, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "time() - max(process_start_time_seconds{job=\"argocd-server-metrics\",namespace=~\"$namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Uptime", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 5, "y": 1}, "id": 94, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "count(count by (server) (argocd_cluster_info{namespace=~\"$namespace\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "title": "Clusters", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 8, "y": 1}, "id": 75, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_app_info{namespace=~\"$namespace\",dest_server=~\"$cluster\",health_status=~\"$health_status\",sync_status=~\"$sync_status\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "title": "Applications", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 11, "y": 1}, "id": 107, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "count(count by (repo) (argocd_app_info{namespace=~\"$namespace\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "title": "Repositories", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"id": 0, "op": "=", "text": "0", "type": 1, "value": "null"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 14, "y": 1}, "id": 100, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_app_info{namespace=~\"$namespace\",dest_server=~\"$cluster\",operation!=\"\"})", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Operations", "type": "gauge"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 4, "w": 7, "x": 17, "y": 1}, "id": 28, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_app_info{namespace=~\"$namespace\",dest_server=~\"$cluster\",health_status=~\"$health_status\",sync_status=~\"$sync_status\"}) by (namespace)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "Applications", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 77, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 6}, "id": 105, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_app_info{namespace=~\"$namespace\",dest_server=~\"$cluster\",health_status=~\"$health_status\",sync_status=~\"$sync_status\",health_status!=\"\"}) by (health_status)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{health_status}}", "refId": "A"}], "title": "Health Status", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 6}, "id": 106, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_app_info{namespace=~\"$namespace\",dest_server=~\"$cluster\",health_status=~\"$health_status\",sync_status=~\"$sync_status\",health_status!=\"\"}) by (sync_status)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{sync_status}}", "refId": "A"}], "title": "Sync Status", "type": "timeseries"}], "title": "Application Status", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 104, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 3}, "id": 56, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(round(increase(argocd_app_sync_total{namespace=~\"$namespace\",dest_server=~\"$cluster\"}[$interval]))) by ($grouping)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{$grouping}}", "refId": "A"}], "title": "Sync Activity", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 9}, "id": 73, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(round(increase(argocd_app_sync_total{namespace=~\"$namespace\",phase=~\"Error|Failed\",dest_server=~\"$cluster\"}[$interval]))) by ($grouping, phase)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{phase}}: {{$grouping}}", "refId": "A"}], "title": "Sync Failures", "type": "timeseries"}], "title": "Sync Stats", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 64, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 12}, "id": 58, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_app_reconcile_count{namespace=~\"$namespace\",dest_server=~\"$cluster\"}[$interval])) by ($grouping)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{$grouping}}", "refId": "A"}], "title": "Reconciliation Activity", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 18}, "id": 60, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_app_reconcile_bucket{namespace=~\"$namespace\"}[$interval])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 10, "legendFormat": "{{le}}", "refId": "A"}], "title": "Reconciliation Performance", "type": "heatmap"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 25}, "id": 80, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_app_k8s_request_total{namespace=~\"$namespace\",server=~\"$cluster\"}[$interval])) by (verb, resource_kind)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{verb}} {{resource_kind}}", "refId": "A"}], "title": "K8s API Activity", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 31}, "id": 96, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(workqueue_depth{namespace=~\"$namespace\",name=~\"app_.*\"}) by (name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "title": "Workqueue Depth", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 31}, "id": 98, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_kubectl_exec_pending{namespace=~\"$namespace\"}) by (command)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{command}}", "refId": "A"}], "title": "Pending kubectl run", "type": "timeseries"}], "title": "Controller Stats", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 102, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 26}, "id": 34, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_memstats_heap_alloc_bytes{job=\"argocd-metrics\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 33}, "id": 108, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "irate(process_cpu_seconds_total{job=\"argocd-metrics\",namespace=~\"$namespace\"}[1m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 40}, "id": 62, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_goroutines{job=\"argocd-metrics\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "Goroutines", "type": "timeseries"}], "title": "Controller Telemetry", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 88, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 90, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(argocd_cluster_api_resource_objects{namespace=~\"$namespace\",server=~\"$cluster\"}) by (server)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{server}}", "refId": "A"}], "title": "Resource Objects Count", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 34}, "id": 92, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "  sum(argocd_cluster_api_resources{namespace=~\"$namespace\",server=~\"$cluster\"}) by (server)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{server}}", "refId": "A"}], "title": "API Resources Count", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 40}, "id": 86, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_cluster_events_total{namespace=~\"$namespace\",server=~\"$cluster\"}[$interval])) by (server)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{server}}", "refId": "A"}], "title": "Cluster Events Count", "type": "timeseries"}], "title": "Cluster Stats", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 70, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "id": 82, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_git_request_total{request_type=\"ls-remote\", namespace=~\"$namespace\"}[10m])) by (namespace)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "Git Requests (ls-remote)", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 84, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_git_request_total{request_type=\"fetch\", namespace=~\"$namespace\"}[10m])) by (namespace)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{namespace}}", "refId": "A"}], "title": "Git Requests (checkout)", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "id": 114, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_git_request_duration_seconds_bucket{request_type=\"fetch\", namespace=~\"$namespace\"}[$interval])) by (le)", "format": "heatmap", "intervalFactor": 10, "legendFormat": "{{le}}", "refId": "A"}], "title": "<PERSON><PERSON> Performance", "type": "heatmap"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "id": 116, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(argocd_git_request_duration_seconds_bucket{request_type=\"ls-remote\", namespace=~\"$namespace\"}[$interval])) by (le)", "format": "heatmap", "intervalFactor": 10, "legendFormat": "{{le}}", "refId": "A"}], "title": "Git Ls-Remote Performance", "type": "heatmap"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 23}, "id": 71, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_memstats_heap_alloc_bytes{job=\"argocd-repo-server\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "Memory Used", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 31}, "id": 72, "options": {"dataLinks": []}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_goroutines{job=\"argocd-repo-server\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "Goroutines", "type": "timeseries"}], "title": "Repo Server Stats", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 66, "panels": [{"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 89}, "id": 61, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_memstats_heap_alloc_bytes{job=\"argocd-server-metrics\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "Memory Used", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 97}, "id": 36, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_goroutines{job=\"argocd-server-metrics\",namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "Goroutines", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 106}, "id": 38, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "go_gc_duration_seconds{job=\"argocd-server-metrics\", quantile=\"1\", namespace=~\"$namespace\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A"}], "title": "GC Time Quantiles", "type": "timeseries"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 115}, "id": 54, "options": {}, "title": "", "transparent": true, "type": "text"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 117}, "id": 40, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"application.ApplicationService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "ApplicationService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 117}, "id": 42, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"cluster.ClusterService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "ClusterService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 126}, "id": 44, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"project.ProjectService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "ProjectService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 126}, "id": 46, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"repository.RepositoryService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "RepositoryService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 135}, "id": 48, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"session.SessionService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "SessionService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 135}, "id": 49, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"version.VersionService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "VersionService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 144}, "id": 50, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"account.AccountService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "AccountService Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 144}, "id": 99, "options": {}, "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum(increase(grpc_server_handled_total{job=\"argocd-server-metrics\",grpc_service=\"settings.SettingsService\",namespace=~\"$namespace\"}[$interval])) by (grpc_code, grpc_method)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{grpc_code}},{{grpc_method}}", "refId": "A"}], "title": "SettingsService Requests", "type": "timeseries"}], "title": "Server Stats", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 110, "panels": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 9}, "id": 112, "options": {"dataLinks": []}, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(increase(argocd_redis_request_total{namespace=~\"$namespace\"}[$interval])) by (failed)", "refId": "A"}], "title": "Requests by result", "type": "timeseries"}], "title": "Redis Stats", "type": "row"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": ["argocd", "infra"], "templating": {"list": [{"current": {"text": "victoriametrics", "value": "PABDA7AB1AD2A1489"}, "includeAll": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": "$datasource", "definition": "label_values(kube_pod_info, namespace)", "includeAll": true, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_info, namespace)", "refId": "Prometheus-namespace-Variable-Query"}, "refresh": 1, "regex": ".*argocd.*", "type": "query"}, {"auto": true, "auto_count": 30, "auto_min": "1m", "current": {"text": "$__auto", "value": "$__auto"}, "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "2h", "value": "2h"}, {"selected": false, "text": "4h", "value": "4h"}, {"selected": false, "text": "8h", "value": "8h"}], "query": "1m,5m,10m,30m,1h,2h,4h,8h", "refresh": 2, "type": "interval"}, {"current": {"text": "namespace", "value": "namespace"}, "includeAll": false, "name": "grouping", "options": [{"selected": true, "text": "namespace", "value": "namespace"}, {"selected": false, "text": "name", "value": "name"}, {"selected": false, "text": "project", "value": "project"}], "query": "namespace,name,project", "type": "custom"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": "$datasource", "definition": "label_values(argocd_cluster_info, server)", "includeAll": true, "name": "cluster", "options": [], "query": {"query": "label_values(argocd_cluster_info, server)", "refId": "Prometheus-cluster-Variable-Query"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".*", "current": {"text": "$__all", "value": "$__all"}, "includeAll": true, "name": "health_status", "options": [{"selected": false, "text": "Healthy", "value": "Healthy"}, {"selected": false, "text": "Progressing", "value": "Progressing"}, {"selected": false, "text": "Suspended", "value": "Suspended"}, {"selected": false, "text": "Missing", "value": "Missing"}, {"selected": false, "text": "Degraded", "value": "Degraded"}, {"selected": false, "text": "Unknown", "value": "Unknown"}], "query": "Healthy,Progressing,Suspended,Missing,Degraded,Unknown", "type": "custom"}, {"allValue": ".*", "current": {"text": "$__all", "value": "$__all"}, "includeAll": true, "name": "sync_status", "options": [{"selected": false, "text": "Synced", "value": "Synced"}, {"selected": false, "text": "OutOfSync", "value": "OutOfSync"}, {"selected": false, "text": "Unknown", "value": "Unknown"}], "query": "Synced,OutOfSync,Unknown", "type": "custom"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ArgoCD", "uid": "qPkgGHg7k", "version": 2, "weekStart": ""}