{{- define "eyecue.common.pv" -}}
  {{- $rootContext := .rootContext -}}
  {{- $pvObject := .object -}}
  {{- $labels := $pvObject.labels | default dict -}}
  {{- $annotations := $pvObject.annotations | default dict -}}
  {{- if $pvObject.retain }}
    {{- $annotations = merge
      (dict "helm.sh/resource-policy" "keep")
      $annotations
    -}}
  {{- end -}}
---
kind: PersistentVolume
apiVersion: v1
metadata:
  name: {{ $pvObject.name }}
  namespace: {{ include "eyecue.common.namespace" $rootContext }}
  {{- with $labels }}
  labels:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- with $annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  capacity:
    storage: {{ default "1Gi" $pvObject.size | quote }}
  accessModes:
    - {{ default "ReadWriteOnce" $pvObject.accessMode | quote }}
  storageClassName: {{ default "" $pvObject.storageClass | quote }}
  volumeMode: {{ default "Filesystem" $pvObject.volumeMode | quote }}
  persistentVolumeReclaimPolicy: {{ default "Retain" $pvObject.persistentVolumeReclaimPolicy | quote }}

  hostPath:
    path: {{
      required (printf "hostPath.path is required for PV %v" $pvObject.name)
      (tpl $pvObject.hostPath.path $rootContext | quote)
    }}
    type: {{ default "" $pvObject.hostPath.type | quote }}
{{- end -}}
