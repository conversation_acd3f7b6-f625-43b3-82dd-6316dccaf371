{{/* Expand the name of the chart */}}
{{- define "eyecue.common.lib.chart.names.name" -}}
  {{- $globalNameOverride := get (.Values.global | default dict) "nameOverride" -}}
  {{- $nameOverride := get .Values "nameOverride" -}}
  {{- $name := $globalNameOverride | default $nameOverride | default .Chart.Name -}}
  {{- $name | toString | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/* Create chart name and version as used by the chart label */}}
{{- define "eyecue.common.lib.chart.names.chart" -}}
  {{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}
