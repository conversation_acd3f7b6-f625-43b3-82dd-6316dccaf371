{{- define "eyecue.common.configMap" -}}
  {{- $rootContext := .rootContext -}}
  {{- $configMapObject := .object -}}
  {{- $labels := $configMapObject.labels | default dict -}}
  {{- $annotations := $configMapObject.annotations | default dict -}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $configMapObject.name }}
  namespace: {{ include "eyecue.common.namespace" $rootContext }}
  {{- with $labels }}
  labels:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- with $annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
{{- with $configMapObject.data }}
data:
    {{- tpl (toYaml .) $rootContext | nindent 2 }}
{{- end }}
{{- with $configMapObject.binaryData }}
binaryData:
    {{- tpl (toYaml .) $rootContext | nindent 2 }}
{{- end }}
{{- end -}}
