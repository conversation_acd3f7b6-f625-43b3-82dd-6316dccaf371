{{- define "argocd-apps.application" }}
  {{- $rootContext := .rootContext -}}
  {{- $default := $rootContext.Values.appDefaults -}}
  {{- $appName := .name -}}
  {{- $appData := .data -}}
  {{- if $appData.enabled -}}
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  {{- with $appData.additionalAnnotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  {{- with $appData.additionalLabels }}
  labels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: {{ $appName }}
  {{- with $appData.namespace | default $default.namespace }}
  namespace: {{ . }}
  {{- end }}
  {{- with $appData.finalizers | default $default.finalizers }}
  finalizers:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  project: {{ $appData.project }}
  {{- with $appData.source }}
  source:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $appData.sources }}
  sources:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  destination:
    {{- deepCopy $default.destination | merge $appData.destination | toYaml | nindent 4 }}
  {{- with $appData.syncPolicy | default $default.syncPolicy }}
  syncPolicy:
    automated:
      {{- deepCopy $default.syncPolicy.automated | merge .automated | toYaml | nindent 6 }}
    retry:
      {{- deepCopy $default.syncPolicy.retry | merge .retry | toYaml | nindent 6 }}
    {{- with .syncOptions | default $default.syncPolicy.syncOptions }}
    syncOptions:
      {{- toYaml . | nindent 6 }}
    {{- end }}
  {{- end }}
  {{- with $appData.revisionHistoryLimit }}
  revisionHistoryLimit: {{ . }}
  {{- end }}
  {{- with $appData.ignoreDifferences }}
  ignoreDifferences:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $appData.info }}
  info:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
{{- end }}
