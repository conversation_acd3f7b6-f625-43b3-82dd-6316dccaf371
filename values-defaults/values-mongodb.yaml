# yaml-language-server: https://raw.githubusercontent.com/bitnami/charts/refs/tags/mongodb/16.5.15/bitnami/mongodb/values.schema.json
# https://github.com/bitnami/charts/blob/mongodb/16.5.15/bitnami/mongodb/values.yaml
fullnameOverride: eyecue-mongodb
architecture: standalone
useStatefulSet: true

pdb:
  create: false

metrics:
  enabled: true

auth:
  enabled: false

persistence:
  existingClaim: mongodb-pvc

volumePermissions:
  enabled: true

resources:
  requests:
    cpu: 300m
    memory: 1024Mi
  limits:
    memory: 1024Mi

extraDeploy:
  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: mongodb-pvc
    spec:
      accessModes:
        - "ReadWriteOnce"
      resources:
        requests:
          storage: 1Gi
      storageClassName: ""
      volumeName: mongodb-pv
  - kind: PersistentVolume
    apiVersion: v1
    metadata:
      name: mongodb-pv
    spec:
      capacity:
        storage: 1Gi
      accessModes:
        - ReadWriteOnce
      storageClassName: ""
      volumeMode: Filesystem
      persistentVolumeReclaimPolicy: Retain
      hostPath:
        path: /media/fingermark/storage/mongodb-infra
