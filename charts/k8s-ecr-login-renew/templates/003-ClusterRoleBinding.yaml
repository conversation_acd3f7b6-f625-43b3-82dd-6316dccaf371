apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ required "A cluster role binding name is required" .Values.names.clusterRoleBinding }}
{{- if .Values.forHelm }}
  labels:
    app.kubernetes.io/name: {{ .Chart.Name }}
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ required "A cluster role name is requred" .Values.names.clusterRole }}
subjects:
  - kind: ServiceAccount
    name: {{ required "A service account name is required" .Values.names.serviceAccount }}
    namespace: {{ .Release.Namespace | default "default" }}
