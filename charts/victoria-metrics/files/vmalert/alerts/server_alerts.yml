groups:
  - name: server-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
      - alert: EyecueServerLostAssetsChange
        expr: changes(eyecue_server_lost_assets_count_total{}[10m]) > 5
        keep_firing_for: 12h
        labels:
          severity: warning
        annotations:
          summary: "Eyecue server lost assets count suddenly changed in the past 10 minutes"
          description: |
            Eyecue server deleted more than 5 cars on an ROI in the past 10 minutes.
            {{ $labels.siteId }} - {{ $labels.roi_id }} - Current instance: {{ printf "%.2f" $value }}
      - alert: CleanUpJobsFailed
        expr: changes(eyeq_server_clean_up_jobs_crashed_total[20m]) > 2
        labels:
          severity: critical
        annotations:
          summary: "Eyecue server clean up jobs failed."
          description: |
            Eyecue server clean up job failed. This means we might not be producing aggregated data.
            {{ $labels.siteId }} - Current instance: {{ printf "%.2f" $value }}
