# Eyecue Infra Helm Charts

This repository contains the values files for the Helm charts used in the Eyecue Infra project.

The repository is organized in the following way:

```
├── charts
│   ├── argocd-apps
│   ├── eyecue-common
│   ├── eyecue-common-test
│   ├── infra-common
│   ├── k8s-ecr-login-renew
│   └── triton
├── values-defaults
|   ├── values-redis.yaml
|   └── values-mongodb.yaml
└── values-overrides
    ├── redis
    │   ├── site
    |   |   ├── <site-id>.yaml
    │   |   └── fm-tst-aus-0318.yaml
    │   └── organization
    |       ├── <organization-id>.yaml
    |       └── qa-aus.yaml
    └── mongodb
        ├── site
        |   ├── <site-id>.yaml
        |   └── fm-tst-aus-0318.yaml
        └── organization
            ├── <organization-id>.yaml
            └── qa-aus.yaml
```

- The `charts` directory contains the Helm charts for the different components of the Eyecue Infra project.
- The `values-defaults` directory contains the default values for the different components of the Eyecue Infra project.
- The `values-overrides` directory contains the values files that override the default values in the charts.

  These are organized by organization and site which allows us to easily customize the deployments.
  Note that the values under `values-overrides` are entirely optional.

# Getting Started

```bash
helm plugin install https://github.com/hypnoglow/helm-s3.git
helm plugin install https://github.com/helm-unittest/helm-unittest.git

# this is the bucket where the helm packages are stored
export AWS_PROFILE=cv-prod
# pull all dependencies
make helm-dependency-update-all
# lint all charts
make helm-lint-all
# test the charts
make helm-test
```
