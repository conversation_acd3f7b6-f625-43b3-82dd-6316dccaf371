---
# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: Secret Test Suite
templates:
  - classes/secret.yaml
tests:
  - it: Should create a simple secret
    set:
      name: testing
      stringData:
        key1: value1
        key2: value2

    asserts:
      - isKind:
          of: Secret
      - equal:
          path: metadata
          value:
            name: testing
            namespace: NAMESPACE
      - equal:
          path: stringData
          value:
            key1: value1
            key2: value2
      - notExists:
          path: data
