# These are the most common parameters that you might want to change.
apiVersion: batch/v1beta1

# This value is required. It determines which AWS region to use.
awsRegion: null

# Leave these values empty (null) if you are manually pre-creating the AWS secrets
# If you supply these values, a secret will be created from them
awsAccessKeyId: null
awsSecretAccessKey: null

# The name of the secret to create containing the Docker credentials for ECR.
dockerSecretName: k8s-ecr-login-renew-docker-secret

# Comma-separated list of target Namespaces to create docker secrets in.
targetNamespace: "default"
excludeNamespace: null

# By default the tool will create credentials for: https://[ACCOUNT_ID].dkr.ecr.[region].amazonaws.com
# If you need credentials for multiple ECR instances (for different regions for example), you can populate this value with a comma separated list.
# Example: registries: https://ACCOUNT_2_ID.dkr.ecr.us-east-1.amazonaws.com,https://ACCOUNT_2_ID.dkr.ecr.us-east-1.amazonaws.com
registries: null

cronjob:
  dockerImage: nabsul/k8s-ecr-login-renew:v1.7.1

  # the schedule of the cronjob
  schedule: "0 */6 * * *"

  # Success job history limit
  successfulJobsHistoryLimit: 3

  # Failed job history limit
  failedJobsHistoryLimit: 5

  # The deadline afterwhich running the cronJob will be skipped. See https://kubernetes.io/docs/concepts/workloads/controllers/cron-jobs/#cron-job-limitations for defaults and details.
  startingDeadlineSeconds: null

  # Termination grace period Seconds
  terminationGracePeriodSeconds: 0

# Set this to false in order to generate raw YAML that can be used without Helm
# The main difference is that the default Helm labels are not added to the objects.
forHelm: true

# Below are less commonly changed parameters:

# The names of the various Kubernetes objects that will be created
names:
  job: k8s-ecr-login-renew-job
  cronJob: k8s-ecr-login-renew-cron
  serviceAccount: k8s-ecr-login-renew-account
  clusterRole: k8s-ecr-login-renew-role
  clusterRoleBinding: k8s-ecr-login-renew-binding

# AWS is accessed using an access key ID and secret access key.
# You can either pre-populate a Kubernetes secret with this information,
# or provide them as Helm values for the secret to be automatically created.
# Set `aws` to null if you will not be authenticating with environment variables.
aws:
  secretName: 'k8s-ecr-login-renew-aws-secret'
  secretKeys:
    accessKeyId: 'AWS_ACCESS_KEY_ID'
    secretAccessKey: 'AWS_SECRET_ACCESS_KEY'

# Pod annotations for the pods that are created by the cronjob
podAnnotations: {}

# Pod tolerations for the cron job
tolerations: {}
