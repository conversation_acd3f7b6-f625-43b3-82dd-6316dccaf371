{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Official dashboard for NGINX Prometheus exporter", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 26, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "title": "Status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "#E02F44", "value": null}, {"color": "#FF9830", "value": 1}, {"color": "#299c46", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 1}, "id": 8, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"]}}, "repeat": "instance", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nginx_up{instance=~\"$instance\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "title": "NGINX Status for $instance", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 6, "panels": [], "title": "Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Connections (rate)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 5}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "irate(nginx_connections_accepted{instance=~\"$instance\"}[5m])", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{instance}} accepted", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "irate(nginx_connections_handled{instance=~\"$instance\"}[5m])", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{instance}} handled", "refId": "B"}], "title": "Processed connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Connections", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 5}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nginx_connections_active{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} active", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nginx_connections_reading{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} reading", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nginx_connections_waiting{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} waiting", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nginx_connections_writing{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} writing", "refId": "D"}], "title": "Active Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "irate(nginx_http_requests_total{instance=~\"$instance\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} total requests", "refId": "A"}], "title": "Total requests", "type": "timeseries"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": ["nginx", "prometheus", "nginx prometheus exporter", "infra"], "templating": {"list": [{"current": {"text": "default", "value": "default"}, "includeAll": false, "label": "datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": "PABDA7AB1AD2A1489", "definition": "label_values(nginx_up, instance)", "includeAll": true, "multi": true, "name": "instance", "options": [], "query": "label_values(nginx_up, instance)", "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "NGINX", "uid": "MsjffzSZz", "version": 1, "weekStart": ""}