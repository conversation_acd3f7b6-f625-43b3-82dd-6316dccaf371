args:
  - '--log-verbose=0'
  - '--grpc-infer-allocation-pool-size=16'

minio_url: s3://infra-minio-gateway.infra.svc.cluster.local:9000

# When model_repository_bucket is nil, the GPU model name is used to infer the bucket path.
model_repository_bucket: null
default_model_repository_bucket: eyecue-weights/triton/${BUCKET_GPU_PATH}-cosine-cfa-23.01-py3/models

# Mapping of GPU model names output by nvidia-smi to the model repository paths used in s3
gpu_mapping:
  "Quadro RTX 4000": rtx-4000
  # The RTX A2000 12GB is from the same family as the RTX A4000
  "NVIDIA RTX A4000": rtx-a4000
  "NVIDIA RTX A2000 12GB": rtx-a4000

gpu_bucket_path_script: |
  {{/* Get the GPU model name from nvidia-smi. */}}
  GPU_NAME=$(
    nvidia-smi \
      --query-gpu=gpu_name \
      --format=csv,noheader
  )

  BUCKET_GPU_PATH=$(
    # Get the bucket path from the GPU mapping
    echo "$GPU_MAPPING" | grep -E "^$GPU_NAME:" | sed -E 's/^[^:]+:[[:space:]]*//'
  )

  if [ -z "$BUCKET_GPU_PATH" ]; then
    echo "No bucket path found for GPU model: '${GPU_NAME}'"
    exit 1
  fi

  echo "[INFO] Using bucket path: '${BUCKET_GPU_PATH}' for GPU model: '${GPU_NAME}'"

service:
  type: ClusterIP
  name: infra-triton
  ports:
    # http:
    #   port: 8000
    #   targetPort: 8000
    #   protocol: TCP
    grpc:
      port: 8001
      targetPort: 8001
      protocol: TCP
    metrics:
      port: 8002
      targetPort: 8002
      protocol: TCP

deployment:
  name: infra-triton

  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics"
    prometheus.io/port: "8002"

  volumes:
    - name: weights
      hostPath:
        path: /media/fingermark/storage/weights-23.01/

  containers:
    server:
      name: triton
      image:
        repository: docker.io/fingermarkglobal/eyecue-triton
        tag: 23.01-py3-simplified
      imagePullPolicy: Always
      ports:
        # - containerPort: 8000
        #   name: http
        - containerPort: 8001
          name: grpc
        - containerPort: 8002
          name: metrics
      # TODO: change these to use the gRPC or HTTP endpoints
      # The simplified version of the Triton server doesn't have the HTTP
      # endpoint and Kubernetes version 1.20 doesn't support gRPC probes
      livenessProbe:
        initialDelaySeconds: 15
        failureThreshold: 3
        periodSeconds: 10
        httpGet:
          path: /metrics
          port: metrics
      readinessProbe:
        initialDelaySeconds: 5
        periodSeconds: 5
        failureThreshold: 3
        httpGet:
          path: /metrics
          port: metrics
      startupProbe:
        # 60s x 10s = 10 minutes
        periodSeconds: 10
        failureThreshold: 60
        httpGet:
          path: /metrics
          port: metrics
      resources:
        requests:
          cpu: "2"
          memory: 2Gi
        limits:
          cpu: "4"
          memory: 4Gi
          # nvidia.com/gpu: 1
      volumeMounts:
        - name: weights
          mountPath: /models/
      env:
        - name: GPU_MAPPING
          value: |
            {{- .Values.gpu_mapping | toYaml | nindent 4 }}
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: infra-minio-gateway-auth-secret
              key: root-user
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: infra-minio-gateway-auth-secret
              key: root-password
      command:
        - "/bin/sh"
        - "-c"
        - |
          set -e

          {{- $defaultModelsBucket := .Values.default_model_repository_bucket }}
          {{- $modelsBucket := .Values.model_repository_bucket }}
          {{- $minioUrl := .Values.minio_url }}
          {{- $bucketIsSlice := kindIs "slice" $modelsBucket }}

          {{- /*
            Here it gets a bit convoluted

            The logic is as follows:
            - If model_repository_bucket is a list, we use multiple model repositories, so
              we iterate over the model_repository_bucket and add each model repository.
            - Else if model_repository_bucket is local, just use the local path.
            - Else if model_repository_bucket is nil, automatically infer the
              model_repository_bucket based on the BUCKET_GPU_PATH.
            - Else, just use the model_repository_bucket as is.
          */}}
          {{- $modelRepositories := list -}}
          {{- if $bucketIsSlice -}}
            {{- range $bucket := $modelsBucket -}}
              {{- /* If the bucket is a slice, we assume it's a list of model repositories. */}}
              {{- $modelRepositories = append $modelRepositories (printf "%s/%s" $minioUrl $bucket) -}}
            {{- end -}}
          {{- else if eq $modelsBucket "local" -}}
            {{- $modelRepositories = append $modelRepositories "/models/" -}}
          {{- else if not $modelsBucket -}}
            {{- /* Get the GPU model name from nvidia-smi. */}}
            {{- tpl .Values.gpu_bucket_path_script . | nindent 2 -}}
            {{- $modelRepositories = append $modelRepositories (printf "%s/%s" $minioUrl $defaultModelsBucket) -}}
          {{- else -}}
            {{- $modelRepositories = append $modelRepositories (printf "%s/%s" $minioUrl $modelsBucket) -}}
          {{- end }}

          tritonserver \
            {{- /* Add the args from the values.yaml */}}
            {{- range $arg := .Values.args }}
              {{- $arg | nindent 4 }} \
            {{- end }}
            {{- /* Add the model repositories */}}
            {{- range $index, $modelRepository := $modelRepositories }}
              {{- $isLast := eq (add $index 1) (len $modelRepositories) }}
              {{- if $isLast }}
                {{- printf "--model-repository=%s" $modelRepository | nindent 4 }}
              {{- else }}
                {{- printf "--model-repository=%s" $modelRepository | nindent 4 }} \
              {{- end }}
            {{- end }}
