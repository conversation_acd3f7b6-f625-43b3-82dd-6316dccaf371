{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "MinIO Grafana Dashboard - https://min.io/", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 13502, "graphTooltip": 0, "id": 25, "links": [{"icon": "external link", "includeVars": true, "keepTime": true, "tags": ["minio"], "type": "dashboards"}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 0}, "id": 1, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "time() - max(minio_node_process_starttime_seconds{job=~\"$scrape_jobs\"})", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 0}, "id": 65, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (instance) (minio_s3_traffic_received_bytes{job=~\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Total S3 Ingress", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Free"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 4, "x": 6, "y": 0}, "id": 50, "interval": "1m", "maxDataPoints": 100, "options": {"displayLabels": [], "legend": {"displayMode": "table", "placement": "bottom", "showLegend": true, "values": ["percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "topk(1, sum(minio_cluster_capacity_usable_total_bytes{job=~\"$scrape_jobs\"}) by (instance)) - topk(1, sum(minio_cluster_capacity_usable_free_bytes{job=~\"$scrape_jobs\"}) by (instance))", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "Used", "refId": "A", "step": 300}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "topk(1, sum(minio_cluster_capacity_usable_free_bytes{job=~\"$scrape_jobs\"}) by (instance)) ", "hide": false, "interval": "1m", "legendFormat": "Free", "refId": "B"}], "title": "Capacity", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Objects"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Usage"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 6, "x": 10, "y": 0}, "id": 68, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "max(minio_cluster_usage_total_bytes{job=~\"$scrape_jobs\"})", "interval": "", "legendFormat": "Usage", "range": true, "refId": "A"}], "title": "Data Usage Growth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 16, "y": 0}, "id": 52, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "code", "exemplar": true, "expr": "minio_cluster_objects_size_distribution{job=~\"$scrape_jobs\"}", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{range}}", "refId": "A", "step": 300, "useBackend": false}], "title": "Object Size Distribution", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 0}, "id": 61, "maxDataPoints": 100, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "minio_node_file_descriptor_open_total{job=~\"$scrape_jobs\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{server}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Open FDs ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 3}, "id": 64, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (instance) (minio_s3_traffic_sent_bytes{job=~\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Total S3 Egress", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 3}, "id": 62, "maxDataPoints": 100, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "minio_node_go_routine_total{job=~\"$scrape_jobs\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{server}}", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Goroutines", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bool_on_off"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 6}, "id": 94, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "code", "exemplar": true, "expr": "minio_cluster_health_status{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "includeNullMetadata": true, "interval": "", "legendFormat": "Pool: {{pool}} Set: {{set}}", "range": true, "refId": "A", "useBackend": false}], "title": "Cluster Health Status", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 6}, "id": 78, "maxDataPoints": 100, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": false, "expr": "max(minio_cluster_drive_online_total{job=~\"$scrape_jobs\"})", "format": "time_series", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": ".", "metric": "process_start_time_seconds", "range": false, "refId": "A", "step": 60}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": false, "expr": "max(minio_cluster_drive_offline_total{job=~\"$scrape_jobs\"})", "format": "time_series", "hide": false, "instant": true, "legendFormat": ".", "range": false, "refId": "B"}], "title": "Total Online/Offline Drives", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-yellow", "value": 75000000}, {"color": "dark-red", "value": 100000000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 6}, "id": 66, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "max(minio_cluster_bucket_total{job=~\"$scrape_jobs\"})", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Buckets", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binBps"}, "overrides": []}, "gridPos": {"h": 6, "w": 7, "x": 9, "y": 6}, "id": 63, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (rate(minio_s3_traffic_received_bytes{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "Data Received [{{server}}]", "refId": "A"}], "title": "S3 API Ingress Rate ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binBps"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 6}, "id": 70, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (rate(minio_s3_traffic_sent_bytes{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "Data Sent [{{server}}]", "refId": "A"}], "title": "S3 API Egress Rate ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 8}, "id": 53, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "max(minio_cluster_nodes_online_total{job=~\"$scrape_jobs\"})", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Total Online Servers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-yellow", "value": 75000000}, {"color": "dark-red", "value": 100000000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 9}, "id": 44, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "max(minio_cluster_usage_object_total{job=~\"$scrape_jobs\"})", "format": "time_series", "instant": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Objects", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 10}, "id": 80, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "max(minio_heal_time_last_activity_nano_seconds{job=~\"$scrape_jobs\"})", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Time Since Last Heal", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 10}, "id": 81, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "max(minio_usage_last_activity_nano_seconds{job=~\"$scrape_jobs\"})", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "process_start_time_seconds", "refId": "A", "step": 60}], "title": "Time Since Last Scan", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Errors"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Requests"}, "properties": [{"id": "color", "value": {"fixedColor": "light-green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 9, "x": 0, "y": 12}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server,api) (increase(minio_s3_requests_total{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server,api}}", "refId": "A"}], "title": "S3 API Request Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Errors"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Requests"}, "properties": [{"id": "color", "value": {"fixedColor": "light-green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 7, "x": 9, "y": 12}, "id": 88, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server,api) (increase(minio_s3_requests_4xx_errors_total{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server,api}}", "refId": "A"}], "title": "S3 API Request Error Rate (4xx)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Errors"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "S3 Requests"}, "properties": [{"id": "color", "value": {"fixedColor": "light-green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 12}, "id": 86, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server,api) (increase(minio_s3_requests_5xx_errors_total{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server,api}}", "refId": "A"}], "title": "S3 API Request Error Rate (5xx)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 99, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "builder", "expr": "minio_cluster_health_erasure_set_online_drives{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool {{pool}} / Set {{set}} - Online Drives", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "builder", "expr": "minio_cluster_health_erasure_set_read_quorum{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool {{pool}} / Set {{set}} - Read Quorum", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "builder", "expr": "minio_cluster_health_erasure_set_write_quorum{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool {{pool}} / Set {{set}} - Write Quorum", "range": true, "refId": "C", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "builder", "expr": "minio_cluster_health_erasure_set_healing_drives{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool {{pool}} / Set {{set}} - Healing Drives", "range": true, "refId": "D", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "disableTextWrap": false, "editorMode": "builder", "expr": "minio_cluster_health_erasure_set_status{job=~\"$scrape_jobs\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool {{pool}} / Set {{set}} - Status", "range": true, "refId": "E", "useBackend": false}], "title": "Health Breakdown", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 76, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": false, "expr": "minio_node_process_resident_memory_bytes{job=~\"$scrape_jobs\"}", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{server}}", "range": true, "refId": "A"}], "title": "Memory Usage ", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 26}, "id": 73, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_io_rchar_bytes{job=~\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "Node RChar [{{server}}]", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_io_wchar_bytes{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Node WChar [{{server}}]", "refId": "B"}], "title": "Read, Write I/O", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 85}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 26}, "id": 77, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "rate(minio_node_process_cpu_total_seconds{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "{{server}}", "range": true, "refId": "A"}], "title": "CPU Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Total number of bytes received and sent on MinIO cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 33}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "exemplar": true, "expr": "rate(minio_inter_node_traffic_sent_bytes{job=~\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Internode Bytes Received [{{server}}]", "metric": "minio_http_requests_duration_seconds_count", "range": true, "refId": "A", "step": 4}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_inter_node_traffic_received_bytes{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Internode Bytes Sent [{{server}}]", "refId": "B"}], "title": "Internode Traffic", "type": "timeseries"}, {"aliasColors": {"available **********:9000": "green", "used **********:9000": "blue"}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"links": [], "unit": "bytes", "unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "minio_node_file_descriptor_open_total{job=~\"$scrape_jobs\"}", "interval": "", "legendFormat": "Open FDs [{{server}}]", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "File Descriptors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "bytes", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"Offline **********:9000": "dark-red", "Total **********:9000": "blue"}, "autoMigrateFrom": "graph", "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Number of online drives per MinIO Server", "fieldConfig": {"defaults": {"links": [], "unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 40}, "hiddenSeries": false, "id": 11, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_syscall_read_total{job=~\"$scrape_jobs\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Read Syscalls [{{server}}]", "metric": "process_start_time_seconds", "refId": "A", "step": 60}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_syscall_write_total{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "", "legendFormat": "Write Syscalls [{{server}}]", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Syscalls", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:185", "decimals": 0, "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:186", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 40}, "hiddenSeries": false, "id": 95, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_scanner_objects_scanned{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "1m", "legendFormat": "[{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Scanned Objects", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 47}, "hiddenSeries": false, "id": 75, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_scanner_versions_scanned{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "1m", "legendFormat": "[{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Scanned Versions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 47}, "hiddenSeries": false, "id": 96, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_scanner_directories_scanned{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "1m", "legendFormat": "[{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Scanned Directories", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}, "unit": "dtdurations", "unitScale": true}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 54}, "id": 89, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "minio_cluster_kms_uptime{job=~\"$scrape_jobs\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "minio_cluster_kms_uptime", "refId": "A", "step": 60}], "title": "KMS Uptime", "type": "stat"}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 54}, "hiddenSeries": false, "id": 91, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (increase(minio_cluster_kms_request_error{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "KMS Request 4xx Error Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:332", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unit": "bool_on_off", "unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 54}, "hiddenSeries": false, "id": 90, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (minio_cluster_kms_online{job=~\"$scrape_jobs\"})", "interval": "1m", "legendFormat": "{{server}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "KMS Online(1)/Offline(0)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "bool_on_off", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 54}, "hiddenSeries": false, "id": 98, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_scanner_bucket_scans_finished{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "1m", "legendFormat": "[{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Bucket Scans Finished", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 58}, "hiddenSeries": false, "id": 92, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (increase(minio_cluster_kms_request_failure{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "{{server}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "KMS Request 5xx Error Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:332", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"S3 Errors": "light-red", "S3 Requests": "light-green"}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 58}, "hiddenSeries": false, "id": 93, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum by (server) (rate(minio_cluster_kms_request_success{job=~\"$scrape_jobs\"}[$__rate_interval]))", "interval": "1m", "intervalFactor": 2, "legendFormat": "KMS Request Success [{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "KMS Request Success Rate ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:331", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:332", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"unitScale": true}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 61}, "hiddenSeries": false, "id": 97, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "10.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "rate(minio_node_scanner_bucket_scans_started{job=~\"$scrape_jobs\"}[$__rate_interval])", "interval": "1m", "legendFormat": "[{{server}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "<PERSON><PERSON>ed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:212", "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:213", "format": "none", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 39, "tags": ["minio", "infra"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "definition": "label_values(job)", "hide": 0, "includeAll": true, "multi": true, "name": "scrape_jobs", "options": [], "query": {"query": "label_values(job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "MinIO Dashboard", "uid": "TgmJnqnnj", "version": 1, "weekStart": ""}