groups:
  # Alerts group for VM single assumes that Grafana dashboard
  # https://grafana.com/grafana/dashboards/10229 is installed.
  # Pls update the `dashboard` annotation according to your setup.
  - name: tracker-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
      # - alert: EyecueTrackerFrameGrabberWarningFast
      #   expr: eyecue_tracker_frames_per_second_gauge_framegrabber{} > 8
      #   for: 30m
      #   labels:
      #     severity: warning
      #   annotations:
      #     summary: "Instance {{ $labels.siteId }} - Eyecue Tracker Frame Grabber is running too fast"
      #     description: |
      #       The Framegrabber {{ $labels.tracker_id }} on {{ $labels.siteId }} is running too fast. That can cause problems with the tracker.
      #       Current instance: {{ printf "%.2f" $value }} FPS

      # - alert: EyecueTrackerFrameGrabberWarningSlow
      #   expr: eyecue_tracker_frames_per_second_gauge_framegrabber{} < 4
      #   for: 30m
      #   labels:
      #     severity: warning
      #   annotations:
      #     summary: "Instance {{ $labels.siteId }} - Eyecue Tracker Frame Grabber is running too slow"
      #     description: |
      #       The Framegrabber {{ $labels.tracker_id }} on {{ $labels.siteId }} is running too slow. That can cause problems with the tracker.
      #       Current instance: {{ printf "%.2f" $value }} FPS

      - alert: EyecueTrackerDetectionTimeout
        expr: sum_over_time(eyeq_tracker_detector_timeout_counter_total{}[30m]) > 3
        labels:
          severity: warning
        annotations:
          summary: "Eyecue tracker {{ $labels.tracker_id}} timed out."
          description: |
            The Tracker {{ $labels.tracker_id }} on {{ $labels.siteId }} didn't receive a response from detector over 1s.
            Restarts: {{ printf "%.2f" $value }}
            Reason: {{ $labels.timeout_type }}

            More context on the error: https://www.notion.so/fingermark/EyecueTrackerDetectionTimeout-f0edcaee6d444f78b34b97d22eee563f

      - alert: EyecueTrackerNoStreamAvailable
        expr: sum_over_time(eyecue_tracker_no_stream_available_counter_total{}[1h]) > 10
        for: 24h
        labels:
          severity: critical
        annotations:
          summary: "Eyecue tracker {{ $labels.tracker_id}} not able to read stream."
          description: |
            The Tracker {{ $labels.tracker_id }} on {{ $labels.siteId }} wasn't able to read camera stream repeatedly in 10 mins.
            Restarts: {{ printf "%.2f" $value }}
            Status: {{ $labels.status }}

            More context on the error: https://www.notion.so/fingermark/EyecueTrackerNoStreamAvailable-0882f000b8614ddd99f72da373099e60

      - alert: EyecueTrackerNoStreamAvailable
        expr: sum_over_time(eyecue_tracker_no_stream_available_counter_total{}[10m]) > 3
        labels:
          severity: warning
        annotations:
          summary: "Eyecue tracker {{ $labels.tracker_id}} not able to read stream."
          description: |
            The Tracker {{ $labels.tracker_id }} on {{ $labels.siteId }} wasn't able to read camera stream repeatedly in 10 mins.
            Restarts: {{ printf "%.2f" $value }}
            Status: {{ $labels.status }}

            More context on the error: https://www.notion.so/fingermark/EyecueTrackerNoStreamAvailable-0882f000b8614ddd99f72da373099e60

      - alert: EyecueTrackerRestarts
        expr: sum_over_time(eyecue_tracker_restarts_eyecue_tracker_total{}[10m]) > 3
        labels:
          severity: warning
        annotations:
          summary: "Instance {{ $labels.siteId }} - {{ $labels.tracker_id}} Eyecue tracker restart too many times"
          description: |
            The Tracker {{ $labels.tracker_id }} on {{ $labels.siteId }} has repeatedly restart in 10 mins.
            Restarts: {{ printf "%.2f" $value }}

            More context on the error: https://www.notion.so/fingermark/EyecueTrackerRestarts-6f422162446c4a63b99bff4e2de28d16

      - alert: EyecueTrackerRestarts
        expr: sum_over_time(eyecue_tracker_restarts_eyecue_tracker_total{}[1h]) > 10
        for: 24h
        labels:
          severity: critical
        annotations:
          summary: "Instance {{ $labels.siteId }} - {{ $labels.tracker_id}} Eyecue tracker restart too many times in 24 hours"
          description: |
            The Tracker {{ $labels.tracker_id }} on {{ $labels.siteId }} has repeatedly restart.
            Restarts: {{ printf "%.2f" $value }}

            More context on the error: https://www.notion.so/fingermark/EyecueTrackerRestarts-6f422162446c4a63b99bff4e2de28d16
