global:
  resolve_timeout: 5m
  slack_api_url: "*******************************************************************************" # eyecue-ops-alerts

route:
  group_by: ["..."]
  group_wait: 30s
  group_interval: 30s
  repeat_interval: 8h
  receiver: default-receiver
  routes:
    - match:
        severity: critical
      receiver: critical-receiver
      mute_time_intervals:
        - offhours_1
        - offhours_2
        - offhours_3

receivers:
  - name: default-receiver
    slack_configs:
      - channel: "#eyecue-ops-alerts"
        send_resolved: true
        username: Alertmanager
        color: '{{ template "slack.color" . }}'
        title: '{{ template "slack.title" . }}'
        text: '{{ template "slack.text" . }}'

  - name: critical-receiver
    slack_configs:
      - channel: "#eyecue-ops-alerts-critical"
        send_resolved: true
        api_url: *******************************************************************************
        username: Alertmanager
        color: '{{ template "slack.color" . }}'
        title: '{{ template "slack.title" . }}'
        text: '{{ template "slack.text" . }}'

templates: ["/config/*.tmpl"]
time_intervals:
  - name: offhours_1
    time_intervals:
      - times:
          - start_time: "00:00"
            end_time: "06:00"
        weekdays: [monday, tuesday, wednesday, thursday, friday, saturday]
        location: "Local"
  - name: offhours_2
    time_intervals:
      - times:
          - start_time: "22:00"
            end_time: "23:59"
        weekdays: [monday, tuesday, wednesday, thursday, friday, saturday]
        location: "Local"
  - name: offhours_3
    time_intervals:
      - times:
          - start_time: "00:00"
            end_time: "23:59"
        weekdays: [sunday]
        location: "Local"
