groups:
  - name: queuezone-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
    - alert: QueueZoneNotSentAlert
      expr: |
        sum(changes(queue_zone_messages_sent_total{}[5m])) < 1
      labels:
        severity: critical
      annotations:
        summary: "Queue zone on {{ $labels.siteId }} did not send any message for 5 minutes."
        description: |
          Queue zone on {{ $labels.siteId }} did not send any message for 5 minutes.

    # - alert: RequiredZoneMissing
    #   expr: |
    #     queue_zone_required_zone_missing{zone_name!=""} == 1
    #   labels:
    #     severity: critical
    #   annotations:
    #     summary: "Required zone missing on {{ $labels.siteId }}"
    #     description: |
    #       The {{ $labels.zone_name }} zone is missing in the danger-zones message on {{ $labels.siteId }}.

    # - alert: QueueRoiStale
    #   expr: |
    #     queue_zone_queue_roi_stale{zone_name!="", roi_id!=""} == 1
    #   labels:
    #     severity: critical
    #   annotations:
    #     summary: "Queue ROI has gone stale on {{ $labels.siteId }}"
    #     description: |
    #       The {{ $labels.roi_id }} in the functinoal zone {{ $labels.zone_name }} of  {{ $labels.siteId }} has gone stale (not received any queue status messages for over 1 minute). Please fix the queue ROI or restart the queue-zone module.
