---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: vms-local-storage-pv
  labels:
    app.kubernetes.io/instance: infra
    app.kubernetes.io/name: vms
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  volumeMode: Filesystem
  persistentVolumeReclaimPolicy: Retain
  claimRef:
    name: vms-local-storage
    namespace: infra
  hostPath:
    path: /media/fingermark/storage/vms-local-storage
    type: DirectoryOrCreate

---
apiVersion: v1
kind: Secret
metadata:
  name: ngc-docker-reg-secret
  labels:
    app.kubernetes.io/instance: infra
    app.kubernetes.io/name: vms
type: kubernetes.io/dockerconfigjson
stringData:
  .dockerconfigjson: |
    {
      "auths": {
        "nvcr.io": {
          "auth": "********************************************************************************************************************************"
        }
      }
    }
