# https://github.com/bitnami/charts/blob/node-exporter/4.5.14/bitnami/node-exporter/values.yaml
pdb:
  create: false

# Disabled as I couldn't figure out why VM Agent was able to scrape
# the metrics on some sites but not others when this was enabled.
# So we may be missing some network related metrics but can revisit
# later if these are deemed necessary.
hostNetwork: false
initContainers:
  # This is required to allow the node-exporter to read the serial number
  # of the machine, which is useful for the support team.
  - name: init-dmi-permissions
    image: docker.io/busybox:1.37
    securityContext:
      runAsUser: 0
      privileged: true
    command: ["sh", "-c", "chmod 444 /sys/class/dmi/id/product_serial"]
    volumeMounts:
      - name: sys
        mountPath: /sys

extraArgs:
  path.rootfs: /host/root

extraVolumeMounts:
  - name: root
    mountPath: /host/root
    readOnly: true

extraVolumes:
  - name: root
    hostPath:
      path: /
