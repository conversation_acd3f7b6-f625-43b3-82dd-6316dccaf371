fullnameOverride: x509-certificate-exporter

# Disable Prometheus operator resources since we're using Victoria Metrics
prometheusRules:
  create: false
prometheusPodMonitor:
  create: false
prometheusServiceMonitor:
  create: false

# Enable TLS Secrets monitoring (Deployment)
secretsExporter:
  enabled: true
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"
    prometheus.io/path: "/metrics"

# Service configuration with prometheus annotations for Victoria Metrics discovery
service:
  create: true
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"
    prometheus.io/path: "/metrics"

# Enable host path monitoring (DaemonSet)
hostPathsExporter:
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"
    prometheus.io/path: "/metrics"

  daemonSets:
    nodes:
      watchFiles:
        - /var/lib/kubelet/pki/kubelet-client-current.pem
        - /etc/kubernetes/pki/apiserver.crt
        - /etc/kubernetes/pki/apiserver-etcd-client.crt
        - /etc/kubernetes/pki/apiserver-kubelet-client.crt
        - /etc/kubernetes/pki/ca.crt
        - /etc/kubernetes/pki/front-proxy-ca.crt
        - /etc/kubernetes/pki/front-proxy-client.crt
        - /etc/kubernetes/pki/etcd/ca.crt
        - /etc/kubernetes/pki/etcd/healthcheck-client.crt
        - /etc/kubernetes/pki/etcd/peer.crt
        - /etc/kubernetes/pki/etcd/server.crt

      watchKubeconfFiles:
        - /etc/kubernetes/admin.conf
        - /etc/kubernetes/controller-manager.conf
        - /etc/kubernetes/scheduler.conf
