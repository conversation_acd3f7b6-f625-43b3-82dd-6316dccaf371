---
siteId: null
organization: null
timezone: null

appDefaults:
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true
    syncOptions:
      - CreateNamespace=true
      - PruneLast=true
    retry:
      limit: 5
  destination:
    server: https://kubernetes.default.svc

applications:
  # ==========================================
  # Eyecue
  # ==========================================
  eyecue:
    enabled: true
    project: eyecue
    syncPolicy:
      automated:
        prune: true
        selfHeal: true
        allowEmpty: true
      syncOptions:
        # The namespace is created in the eyecue helm chart.
        # We disable it here to avoid conflicts.
        - CreateNamespace=false
        - PruneLast=true
      retry:
        limit: 5
    source:
      repoURL: *****************:fingermarkltd/eyecue-qa-helm.git
      targetRevision: HEAD
      path: "{{ .Values.siteId }}"
      helm:
        version: v3
        releaseName: eyecue
        values: |
          eyecue:
            minio:
              enabled: false
            # TODO(CW): deploy these through the argocd infra project
            mongodb:
              enabled: true
            rabbitmq:
              enabled: true
        valueFiles:
          - values.yaml
    destination:
      namespace: nmp-{{ .Values.siteId }}

  # ==========================================
  # Infra
  # ==========================================
  infra-common:
    enabled: false
    project: infra
    source:
      repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
      path: charts/infra-common
      targetRevision: init  # TODO: Change to HEAD
      helm:
        version: v3
        releaseName: infra
        ignoreMissingValueFiles: true
        valueFiles:
          - ../../values-overrides/common/organization/{{ .Values.organization }}.yaml
          - ../../values-overrides/common/site/{{ .Values.siteId }}.yaml
    destination:
      namespace: infra

  k8s-ecr-login-renew:
    enabled: false
    project: infra
    source:
      repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
      path: charts/k8s-ecr-login-renew
      targetRevision: init  # TODO: Change to HEAD
      helm:
        version: v3
        releaseName: infra
        ignoreMissingValueFiles: true
        valueFiles:
          - ../../values-defaults/values-k8s-ecr-login-renew.yaml
          - ../../values-overrides/k8s-ecr-login-renew/organization/{{ .Values.organization }}.yaml
          - ../../values-overrides/k8s-ecr-login-renew/site/{{ .Values.siteId }}.yaml
    destination:
      namespace: infra

  triton:
    enabled: false
    project: infra
    source:
      repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
      path: charts/triton
      targetRevision: init  # TODO: Change to HEAD
      helm:
        version: v3
        releaseName: infra
        ignoreMissingValueFiles: true
        valueFiles:
          - ../../values-overrides/triton/organization/{{ .Values.organization }}.yaml
          - ../../values-overrides/triton/site/{{ .Values.siteId }}.yaml
    destination:
      namespace: infra

  victoria-metrics:
    enabled: false
    project: infra
    source:
      repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
      path: charts/victoria-metrics
      targetRevision: init  # TODO: Change to HEAD
      helm:
        version: v3
        releaseName: infra
        ignoreMissingValueFiles: true
        valueFiles:
          - ../../values-defaults/values-victoria-metrics.yaml
          - ../../values-overrides/victoria-metrics/organization/{{ .Values.organization }}.yaml
          - ../../values-overrides/victoria-metrics/site/{{ .Values.siteId }}.yaml
    destination:
      namespace: infra

  # ===== NVIDIA Helm Charts =====
  nvidia-vst:
    enabled: false
    project: infra
    sources:
      # The NVIDIA VST chart is private and requires authentication, so it is mirrored to S3.
      # To add a new version of the chart, run the following commands:
      #   NGC_API_KEY=<your_ngc_api_key>
      #   NGC_VST_VERSION=1.2.54
      #   helm fetch https://helm.ngc.nvidia.com/nvidia/ace/charts/vms-${NGC_VST_VERSION}.tgz --username='$oauthtoken' --password=${NGC_API_KEY}
      #   helm repo add nvidia-vst s3://eyecue-helm-cv-prod-package/nvidia-vst
      #   helm s3 push --relative vms-${NGC_VST_VERSION}.tgz nvidia-vst
      - repoURL: s3://eyecue-helm-cv-prod-package/nvidia-vst
        chart: vms
        targetRevision: 1.2.54
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          values: |
            configs:
              vst_config.json:
                data:
                  device_location: "{{ .Values.siteId }}"
          valueFiles:
            - $values/values-defaults/values-nvidia-vst.yaml
            - $values/values-overrides/nvidia-vst/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/nvidia-vst/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        path: resources
        directory:
          include: nvidia-vst-extra.yaml
    destination:
      namespace: infra

  # ===== Bitnami Helm Charts =====
  grafana:
    enabled: false
    project: infra
    sources:
      - repoURL: https://grafana.github.io/helm-charts
        chart: grafana
        targetRevision: 8.6.2
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-grafana.yaml
            - $values/values-overrides/grafana/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/grafana/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: init  # TODO: Change to HEAD
        ref: values
    destination:
      namespace: infra

  redis:
    enabled: false
    project: infra
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: redis
        targetRevision: 20.2.1
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-redis.yaml
            - $values/values-overrides/redis/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/redis/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: init  # TODO: Change to HEAD
        ref: values
    destination:
      namespace: infra

  nginx:
    enabled: false
    project: infra
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: nginx
        targetRevision: 18.2.4
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-nginx.yaml
            - $values/values-overrides/nginx/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/nginx/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: init  # TODO: Change to HEAD
        ref: values
    destination:
      namespace: infra

  mongodb:
    enabled: false
    project: infra
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: mongodb
        targetRevision: 16.5.15
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-mongodb.yaml
            - $values/values-overrides/mongodb/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/mongodb/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
    destination:
      namespace: infra

  rabbitmq:
    enabled: false
    project: infra
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: rabbitmq
        targetRevision: 15.0.3
        helm:
          version: v3
          releaseName: infra
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-rabbitmq.yaml
            - $values/values-overrides/rabbitmq/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/rabbitmq/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: init  # TODO: Change to HEAD
        ref: values
    destination:
      namespace: infra

  minio:
    enabled: true
    project: infra
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: minio
        targetRevision: 16.0.10
        helm:
          version: v3
          releaseName: eyecue
          ignoreMissingValueFiles: true
          values: |
            auth:
              rootUser: "{{ .Values.siteId }}"
              rootPassword: "{{ .Values.siteId }}"
          valueFiles:
            - $values/values-defaults/values-minio.yaml
            - $values/values-overrides/minio/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/minio/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
    destination:
      namespace: infra

  # ==========================================
  # Monitoring
  # ==========================================
  node-exporter:
    enabled: true
    project: monitoring
    ignoreDifferences:
      # There were conflicts with the previous deployment from the eyecue helm chart
      # causing the app to always be out of sync. This fixes that.
      - group: ""
        kind: ""
        jsonPointers:
          - /metadata/labels/helm.sh~1chart
          - /metadata/labels/argocd.argoproj.io~1instance
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: node-exporter
        targetRevision: 4.5.14
        helm:
          version: v3
          releaseName: eyecue
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-node-exporter.yaml
            - $values/values-overrides/node-exporter/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/node-exporter/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
    destination:
      namespace: monitoring

  kube-state-metrics:
    enabled: true
    project: monitoring
    ignoreDifferences:
      # There were conflicts with the previous deployment from the eyecue helm chart
      # causing the app to always be out of sync. This fixes that.
      - group: ""
        kind: ""
        jsonPointers:
          - /metadata/labels/helm.sh~1chart
          - /metadata/labels/argocd.argoproj.io~1instance
    sources:
      - repoURL: registry-1.docker.io/bitnamicharts
        chart: kube-state-metrics
        targetRevision: 5.0.9
        helm:
          version: v3
          releaseName: eyecue
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-kube-state-metrics.yaml
            - $values/values-overrides/kube-state-metrics/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/kube-state-metrics/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
    destination:
      namespace: monitoring

  x509-certificate-exporter:
    enabled: true
    project: monitoring
    sources:
      - repoURL: https://charts.enix.io
        chart: x509-certificate-exporter
        targetRevision: 3.19.1
        helm:
          version: v3
          releaseName: x509-certificate-exporter
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-x509-certificate-exporter.yaml
            - $values/values-overrides/x509-certificate-exporter/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/x509-certificate-exporter/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: EYECUE-2894-platform-investigate-how-to-
        ref: values
    destination:
      namespace: monitoring

  lacework:
    enabled: true
    project: infra
    sources:
      - repoURL: https://lacework.github.io/helm-charts
        chart: lacework-agent
        targetRevision: 7.7.0
        helm:
          version: v3
          releaseName: lacework
          ignoreMissingValueFiles: true
          valueFiles:
            - $values/values-defaults/values-lacework.yaml
            - $values/values-overrides/lacework/organization/{{ .Values.organization }}.yaml
            - $values/values-overrides/lacework/site/{{ .Values.siteId }}.yaml
      - repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
        targetRevision: HEAD
        ref: values
    destination:
      namespace: lacework

projects:
  eyecue:
    enabled: true
    namespace: argocd
    permitOnlyProjectScopedClusters: false
    finalizers:
      - resources-finalizer.argocd.argoproj.io
    description: "Eyecue project"
    sourceRepos:
      - *****************:fingermarkltd/eyecue-qa-helm.git
    destinations:
      - namespace: nmp-{{ .Values.siteId }}
        server: https://kubernetes.default.svc
      # TODO: Remove this once eyecue doesn't create resources in the infra/monitoring namespaces
      - namespace: infra
        server: https://kubernetes.default.svc
      - namespace: monitoring
        server: https://kubernetes.default.svc
    clusterResourceWhitelist:
      - group: '*'
        kind: '*'
    syncWindows:
      - kind: allow
        schedule: '* 4 * * *'
        duration: 1h
        applications:
          - '*'
        manualSync: true
        timeZone: "{{ .Values.timezone }}"

  infra:
    enabled: true
    namespace: argocd
    permitOnlyProjectScopedClusters: false
    finalizers:
      - resources-finalizer.argocd.argoproj.io
    description: "Eyecue Infra project"
    sourceRepos:
      - *****************:fingermarkltd/eyecue-infra-helm-charts.git
      - registry-1.docker.io/bitnamicharts  # OCI repo doesn't include the oci:// prefix
      - https://grafana.github.io/helm-charts
      - https://lacework.github.io/helm-charts
      - s3://eyecue-helm-cv-prod-package/nvidia-vst
      - s3://eyecue-helm-cv-prod-us-package/nvidia-vst
    destinations:
      - namespace: infra
        server: https://kubernetes.default.svc
      - namespace: lacework
        server: https://kubernetes.default.svc
    clusterResourceWhitelist:
      - group: '*'
        kind: '*'
    syncWindows:
      - kind: allow
        schedule: '* 4 * * *'
        duration: 1h
        applications:
          - '*'
        manualSync: true
        timeZone: "{{ .Values.timezone }}"

  monitoring:
    enabled: true
    namespace: argocd
    permitOnlyProjectScopedClusters: false
    finalizers:
      - resources-finalizer.argocd.argoproj.io
    description: "Eyecue Monitoring project"
    sourceRepos:
      - *****************:fingermarkltd/eyecue-infra-helm-charts.git
      - registry-1.docker.io/bitnamicharts  # OCI repo doesn't include the oci:// prefix
      - https://charts.enix.io
    destinations:
      - namespace: monitoring
        server: https://kubernetes.default.svc
    clusterResourceWhitelist:
      - group: '*'
        kind: '*'
    syncWindows:
      - kind: allow
        schedule: '* 4 * * *'
        duration: 1h
        applications:
          - '*'
        manualSync: true
        timeZone: "{{ .Values.timezone }}"
