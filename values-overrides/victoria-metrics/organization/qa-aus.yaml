configMaps:
  vmAlertmanagerConfig:
    name: infra-vm-alertmanager-config
    data:
      alertmanager.yaml: |
        {{- .Files.Get "files/vmalert/alertmanager/alertmanager-dev.yml" | nindent 4 }}

victoriaMetricsAgent:
  remoteWrite:
    # the qa-aus environment does not have a remote write to the AWS instance
    - url: http://infra-victoria-metrics-server.infra.svc.cluster.local:8428/api/v1/write
