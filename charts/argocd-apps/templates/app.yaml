{{- include "argocd-apps.validate" . -}}

{{ range $projectName, $projectData := .Values.projects }}
{{ $project := include "argocd-apps.project" (dict "name" $projectName "data" $projectData) }}
{{ include "common.tplvalues.render" (dict "value" $project "context" $) }}
{{ end }}

{{ range $appName, $appData := .Values.applications }}
{{ $application := include "argocd-apps.application" (dict "name" $appName "data" $appData "rootContext" $) }}
{{ include "common.tplvalues.render" (dict "value" $application "context" $) }}
{{ end }}
