groups:
  - name: roi-suggestor-alerts
    concurrency: 1
    labels:
      siteId: "%{SITE_ID}"
    rules:
      - alert: NoRoiSuggestorImagesGenerated
        expr: absent_over_time(roi_suggestor_assembler_generated_image_timestamp[2d]) == 1
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: "No ROI suggestor heatmap generated."
          description: |
            No ROI suggestor images were generated on site {{ $labels.siteId }} in the last 2d
      - alert: SomeRoiSuggestorImagesNotGenerated
        # man, that line cost me some hours
        expr: now() - max_over_time(timestamp(roi_suggestor_assembler_generated_image_timestamp{heatmap_type!="hvi-heatmap"}) [2h]) > 3600
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Roi suggestor heatmap not generated."
          description: |
            {{ $labels.heatmap_type }} was not generated in the last hour for camera {{ $labels.camera_id }} on site
            {{ $labels.siteId }}
