{{- /*
The container definition included in the Pod.
*/ -}}
{{- define "eyecue.common.lib.container.spec" -}}
  {{- $rootContext := .rootContext -}}
  {{- $containerObject := .containerObject -}}
name: {{ $containerObject.name }}
image: "{{ $containerObject.image.repository }}:{{ $containerObject.image.tag }}"
imagePullPolicy: {{ $containerObject.imagePullPolicy | default "IfNotPresent" }}
{{- with $containerObject.command }}
command:
  {{- include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 2 }}
{{- end -}}
{{- with $containerObject.args }}
args:
  {{- include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 2 }}
{{- end }}
{{- with $containerObject.workingDir }}
workingDir: {{ . | trim }}
{{- end -}}
{{- with $containerObject.securityContext }}
securityContext: {{ toYaml . | trim | nindent 2 }}
{{- end -}}
{{- with $containerObject.lifecycle }}
lifecycle: {{ toYaml . | trim | nindent 2 }}
{{- end -}}
{{- with $containerObject.terminationMessagePath }}
terminationMessagePath: {{ . | trim }}
{{- end -}}
{{- with $containerObject.terminationMessagePolicy }}
terminationMessagePolicy: {{ . | trim }}
{{- end -}}
{{- with $containerObject.env }}
env:
  {{- include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 2 }}
{{- end -}}
{{- with $containerObject.envFrom }}
envFrom:
  {{- include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 2 }}
{{- end -}}
{{- with $containerObject.ports }}
ports:
  {{- range $port := . }}
  - containerPort: {{ $port.containerPort }}
    protocol: {{ $port.protocol | default "TCP" }}
    {{- with $port.name }}
    name: {{ $port.name }}
    {{- end }}
  {{- end }}
{{- end }}
resources: {{- include "eyecue.common.lib.container.resources" (dict "rootContext" $rootContext "resourcesObject" $containerObject.resources) }}
{{- with $containerObject.restartPolicy }}
restartPolicy: {{ . | trim }}
{{- end -}}
{{- with $containerObject.stdin }}
stdin: {{ . }}
{{- end -}}
{{- with $containerObject.tty }}
tty: {{ . }}
{{- end -}}
{{- with $containerObject.volumeMounts }}
volumeMounts:
  {{ include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 2 }}
{{- end -}}
{{- with $containerObject.startupProbe }}
startupProbe: {{ toYaml . | trim | nindent 2 }}
{{- end -}}
{{- with $containerObject.livenessProbe }}
livenessProbe: {{ toYaml . | trim | nindent 2 }}
{{- end -}}
{{- with $containerObject.readinessProbe }}
readinessProbe: {{ toYaml . | trim | nindent 2 }}
{{- end -}}
{{- with $containerObject.imagePullSecrets }}
imagePullSecrets:
  {{- range $imagePullSecret := . }}
  - name: {{ $imagePullSecret }}
  {{- end }}
{{- end -}}
{{- end -}}
