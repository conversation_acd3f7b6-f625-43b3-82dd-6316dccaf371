{"type": "object", "additionalProperties": false, "properties": {"annotations": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "labels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "name": {"type": "string"}, "retain": {"type": "boolean"}, "size": {"type": "string"}, "accessMode": {"type": "string"}, "volumeMode": {"type": "string"}, "persistentVolumeReclaimPolicy": {"type": "string"}, "hostPath": {"type": "object", "additionalProperties": false, "properties": {"path": {"type": "string"}, "type": {"type": "string"}}, "required": ["path"]}}, "required": ["name", "hostPath"]}