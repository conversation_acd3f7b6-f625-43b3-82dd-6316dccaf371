-include .env

VERSION ?= $(error Please set the VERSION variable)
CHART_NAME ?= $(error Please set the CHART_NAME variable)
BUCKET_NAME ?= eyecue-helm-cv-prod-package
HELM_PACKAGE_DIR ?= build
CHARTS_DIR ?= charts

CHARTS := $(shell find $(CHARTS_DIR) -maxdepth 1 -mindepth 1 -type d ! -name '*test' -exec basename {} \;)

# Because of firewall restrictions we use the S3 proxy in production to access
# the S3 bucket. For local development it is easier to use the S3 bucket directly.
replace-proxy-with-s3:
	find . \
		-name 'Chart.yaml' \
		-exec sed -i 's|http://argocd-s3-proxy|s3://eyecue-helm-cv-prod-package|g' {} +

helm-lint:
	helm lint $(CHARTS_DIR)/$(CHART_NAME)

helm-lint-all:
	@set -e; \
	for chart in $(CHARTS); do \
		echo "Linting $$chart"; \
		CHART_NAME=$$chart $(MAKE) -s helm-lint; \
	done

helm-dependency-build:
	helm dependency build $(CHARTS_DIR)/$(CHART_NAME)

helm-dependency-build-all:
	@set -e; \
	for chart in $(CHARTS); do \
		echo "Building chart dependencies for $$dir"; \
  	CHART_NAME=$$chart $(MAKE) -s helm-dependency-build; \
  done

helm-dependency-update:
	helm dependency update $(CHARTS_DIR)/$(CHART_NAME)

helm-dependency-update-all:
	@set -e; \
	for chart in $(CHARTS); do \
		echo "Updating chart dependencies for $$dir"; \
		CHART_NAME=$$chart $(MAKE) -s helm-dependency-update; \
	done

helm-package:
	helm package \
		--destination $(HELM_PACKAGE_DIR) \
		--dependency-update \
		--version $(VERSION) \
		$(CHARTS_DIR)/$(CHART_NAME)

helm-repo-add-s3:
	helm repo add $(BUCKET_NAME)-$(CHART_NAME) s3://$(BUCKET_NAME)/$(CHART_NAME)

helm-s3-init:
	helm s3 init s3://$(BUCKET_NAME)/$(CHART_NAME)

helm-s3-push:
	helm s3 push --relative \
		$(HELM_PACKAGE_DIR)/$(CHART_NAME)-$(VERSION).tgz \
		$(BUCKET_NAME)-$(CHART_NAME)

helm-test:
	CHART_NAME=eyecue-common-test $(MAKE) -s helm-dependency-update
	helm unittest $(CHARTS_DIR)/eyecue-common-test
