# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: PVC Test Suite
templates:
  - classes/pvc.yaml
tests:
  - it: Should create a simple pvc
    set:
      name: testing
      storage: 1Gi
      accessModes:
        - ReadWriteOnce
      storageClassName: ""
      retain: true
    asserts:
      - isKind:
          of: PersistentVolumeClaim
      - equal:
          path: metadata
          value:
            name: testing
            namespace: NAMESPACE
            annotations:
              "helm.sh/resource-policy": keep
      - equal:
          path: spec
          value:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 1Gi
            storageClassName: ""
