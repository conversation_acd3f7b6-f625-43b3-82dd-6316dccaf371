{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "A new RabbitMQ Management Overview", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 16, "links": [{"icon": "doc", "tags": [], "targetBlank": true, "title": "Monitoring with Prometheus & Grafana", "tooltip": "", "type": "link", "url": "https://www.rabbitmq.com/prometheus.html"}], "panels": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "#1F60C4", "value": 10000}, {"color": "#C4162A", "value": 100000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 0}, "id": 64, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_queue_messages_ready * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Ready messages", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": -1}, {"color": "#37872D", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 0}, "id": 62, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_received_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Incoming messages / s", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": 0}, {"color": "#37872D", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 0}, "id": 66, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_global_publishers * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Publishers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": 0}, {"color": "#37872D", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 16, "y": 0}, "id": 37, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_connections * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connections", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": 0}, {"color": "#37872D", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 20, "y": 0}, "id": 40, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_queues * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Queues", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#37872D", "value": null}, {"color": "#1F60C4", "value": 100}, {"color": "#C4162A", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 3}, "id": 65, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_queue_messages_unacked * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Unacknowledged messages", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": -1}, {"color": "#37872D", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 3}, "id": 63, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_redelivered_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) +\nsum(rate(rabbitmq_global_messages_delivered_consume_auto_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) +\nsum(rate(rabbitmq_global_messages_delivered_consume_manual_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) +\nsum(rate(rabbitmq_global_messages_delivered_get_auto_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) +\nsum(rate(rabbitmq_global_messages_delivered_get_manual_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Outgoing messages / s", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": 0}, {"color": "#37872D", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 3}, "id": 41, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_consumers * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Consumers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#C4162A", "value": null}, {"color": "#1F60C4", "value": 0}, {"color": "#37872D", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 16, "y": 3}, "id": 38, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_channels * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Channels", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#1F60C4", "value": null}, {"color": "#37872D", "value": 3}, {"color": "#C4162A", "value": 8}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 20, "y": 3}, "id": 67, "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_build_info * on(instance, job) group_left(rabbitmq_cluster) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Nodes", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 4, "panels": [], "title": "NODES", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "erlang_version"}, "properties": [{"id": "displayName", "value": "Erlang/OTP"}, {"id": "unit", "value": "none"}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)"}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rabbitmq_version"}, "properties": [{"id": "displayName", "value": "RabbitMQ"}, {"id": "unit", "value": "none"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)"}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "displayName", "value": "Host"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rabbitmq_node"}, "properties": [{"id": "displayName", "value": "Node name"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)"}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rabbitmq_cluster"}, "properties": [{"id": "displayName", "value": "Cluster"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "prometheus_client_version"}, "properties": [{"id": "displayName", "value": "prometheus.erl"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "prometheus_plugin_version"}, "properties": [{"id": "displayName", "value": "rabbitmq_prometheus"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}]}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 7}, "id": 69, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "rabbitmq_build_info * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "If the value is zero or less, the memory alarm will be triggered and all publishing connections across all cluster nodes will be blocked.\n\nThis value can temporarily go negative because the memory alarm is triggered with a slight delay.\n\nThe kernel's view of the amount of memory used by the node can differ from what the node itself can observe. This means that this value can be negative for a sustained period of time.\n\nBy default nodes use resident set size (RSS) to compute how much memory they use. This strategy can be changed (see the guides below).\n\n* [Alarms](https://www.rabbitmq.com/alarms.html)\n* [Memory Alarms](https://www.rabbitmq.com/memory.html)\n* [Reasoning About Memory Use](https://www.rabbitmq.com/memory-use.html)\n* [Blocked Connection Notifications](https://www.rabbitmq.com/connection-blocked.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 1, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 0}, {"color": "transparent", "value": 536870912}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "(rabbitmq_resident_memory_limit_bytes * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) -\n(rabbitmq_process_resident_memory_bytes * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Memory available before publishers blocked", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "This metric is reported for the partition where the RabbitMQ data directory is stored.\n\nIf the value is zero or less, the disk alarm will be triggered and all publishing connections across all cluster nodes will be blocked.\n\nThis value can temporarily go negative because the free disk space alarm is triggered with a slight delay.\n\n* [Alarms](https://www.rabbitmq.com/alarms.html)\n* [Disk Space Alarms](https://www.rabbitmq.com/disk-alarms.html)\n* [Disk Space](https://www.rabbitmq.com/production-checklist.html#resource-limits-disk-space)\n* [Persistence Configuration](https://www.rabbitmq.com/persistence-conf.html)\n* [Blocked Connection Notifications](https://www.rabbitmq.com/connection-blocked.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 1, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 1073741824}, {"color": "transparent", "value": 5368709120}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 12, "y": 11}, "id": 8, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rabbitmq_disk_space_available_bytes * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Disk space available before publishers blocked", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "When this value reaches zero, new connections will not be accepted and disk write operations may fail.\n\nClient libraries, peer nodes and CLI tools will not be able to connect when the node runs out of available file descriptors.\n\n* [Open File Handles Limit](https://www.rabbitmq.com/production-checklist.html#resource-limits-file-handle-limit)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 500}, {"color": "transparent", "value": 1000}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 11}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "(rabbitmq_process_max_fds * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) -\n(rabbitmq_process_open_fds * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "File descriptors available", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "When this value reaches zero, new connections will not be accepted.\n\nClient libraries, peer nodes and CLI tools will not be able to connect when the node runs out of available file descriptors.\n\n* [Networking and RabbitMQ](https://www.rabbitmq.com/networking.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 500}, {"color": "transparent", "value": 1000}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 15}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "(rabbitmq_process_max_tcp_sockets * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) -\n(rabbitmq_process_open_tcp_sockets * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "TCP sockets available", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 27, "panels": [], "title": "QUEUED MESSAGES", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Total number of ready messages ready to be delivered to consumers.\n\nAim to keep this value as low as possible. RabbitMQ behaves best when messages are flowing through it. It's OK for publishers to occasionally outpace consumers, but the expectation is that consumers will eventually process all ready messages.\n\nIf this metric keeps increasing, your system will eventually run out of memory and/or disk space. Consider using TTL or Queue Length Limit to prevent unbounded message growth.\n\n* [Queues](https://www.rabbitmq.com/queues.html)\n* [Consumers](https://www.rabbitmq.com/consumers.html)\n* [Queue Length Limit](https://www.rabbitmq.com/maxlength.html)\n* [Time-To-Live and Expiration](https://www.rabbitmq.com/ttl.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 20}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_queue_messages_ready * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages ready to be delivered to consumers", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The total number of messages that are either in-flight to consumers, currently being processed by consumers or simply waiting for the consumer acknowledgements to be processed by the queue. Until the queue processes the message acknowledgement, the message will remain unacknowledged.\n\n* [Queues](https://www.rabbitmq.com/queues.html)\n* [Confirms and Acknowledgements](https://www.rabbitmq.com/confirms.html)\n* [Consumer Prefetch](https://www.rabbitmq.com/consumer-prefetch.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 20}, "id": 19, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rabbitmq_queue_messages_unacked * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages pending consumer acknowledgement", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 11, "panels": [], "title": "INCOMING MESSAGES", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The incoming message rate before any routing rules are applied.\n\nIf this value is lower than the number of messages published to queues, it may indicate that some messages are delivered to more than one queue.\n\nIf this value is higher than the number of messages published to queues, messages cannot be routed and will either be dropped or returned to publishers.\n\n* [Publishers](https://www.rabbitmq.com/publishers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 26}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_received_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages published / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages confirmed by the broker to publishers. Publishers must opt-in to receive message confirmations.\n\nIf this metric is consistently at zero it may suggest that publisher confirms are not used by clients. The safety of published messages is likely to be at risk.\n\n* [Publisher Confirms](https://www.rabbitmq.com/confirms.html#publisher-confirms)\n* [Publisher Confirms and Data Safety](https://www.rabbitmq.com/publishers.html#data-safety)\n* [When Will Published Messages Be Confirmed by the Broker?](https://www.rabbitmq.com/confirms.html#when-publishes-are-confirmed)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 26}, "id": 18, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_confirmed_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages confirmed to publishers / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages received from publishers and successfully routed to the master queue replicas.\n\n* [Queues](https://www.rabbitmq.com/queues.html)\n* [Publishers](https://www.rabbitmq.com/publishers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 31}, "id": 61, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_routed_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages routed to queues / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages received from publishers that have publisher confirms enabled and the broker has not confirmed yet.\n\n* [Publishers](https://www.rabbitmq.com/publishers.html)\n* [Confirms and Acknowledgements](https://www.rabbitmq.com/confirms.html)\n* [When Will Published Messages Be Confirmed by the Broker?](https://www.rabbitmq.com/confirms.html#when-publishes-are-confirmed)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 31}, "id": 12, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_received_confirm_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"} - \nrate(rabbitmq_global_messages_confirmed_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}\n) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages unconfirmed to publishers / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages that cannot be routed and are dropped. \n\nAny value above zero means message loss and likely suggests a routing problem on the publisher end.\n\n* [Unroutable Message Handling](https://www.rabbitmq.com/publishers.html#unroutable)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 0}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/rabbit/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 36}, "id": 34, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_unroutable_dropped_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Unroutable messages dropped / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages that cannot be routed and are returned back to publishers.\n\nSustained values above zero may indicate a routing problem on the publisher end.\n\n* [Unroutable Message Handling](https://www.rabbitmq.com/publishers.html#unroutable)\n* [When Will Published Messages Be Confirmed by the Broker?](https://www.rabbitmq.com/confirms.html#when-publishes-are-confirmed)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 0}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/rabbit/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 36}, "id": 16, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_unroutable_returned_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Unroutable messages returned to publishers / s", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 29, "panels": [], "title": "OUTGOING MESSAGES", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages delivered to consumers. It includes messages that have been redelivered.\n\nThis metric does not include messages that have been fetched by consumers using `basic.get` (consumed by polling).\n\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 42}, "id": 14, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(\n  (rate(rabbitmq_global_messages_delivered_consume_auto_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) +\n  (rate(rabbitmq_global_messages_delivered_consume_manual_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"})\n) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages delivered / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages that have been redelivered to consumers. It includes messages that have been requeued automatically and redelivered due to channel exceptions or connection closures.\n\nHaving some redeliveries is expected, but if this metric is consistently non-zero, it is worth investigating why.\n\n* [Negative Acknowledgement and Requeuing of Deliveries](https://www.rabbitmq.com/confirms.html#consumer-nacks-requeue)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "orange", "value": 20}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 42}, "id": 15, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_redelivered_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages redelivered / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of message deliveries to consumers that use manual acknowledgement mode.\n\nWhen this mode is used, RabbitMQ waits for consumers to acknowledge messages before more messages can be delivered.\n\nThis is the safest way of consuming messages.\n\n* [Consumer Acknowledgements](https://www.rabbitmq.com/confirms.html)\n* [Consumer Prefetch](https://www.rabbitmq.com/consumer-prefetch.html)\n* [Consumer Acknowledgement Modes, Prefetch and Throughput](https://www.rabbitmq.com/confirms.html#channel-qos-prefetch-throughput)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 47}, "id": 20, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_delivered_consume_manual_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages delivered with manual ack / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of message deliveries to consumers that use automatic acknowledgement mode.\n\nWhen this mode is used, RabbitMQ does not wait for consumers to acknowledge message deliveries.\n\nThis mode is fire-and-forget and does not offer any delivery safety guarantees. It tends to provide higher throughput and it may lead to consumer overload  and higher consumer memory usage.\n\n* [Consumer Acknowledgement Modes, Prefetch and Throughput](https://www.rabbitmq.com/confirms.html#channel-qos-prefetch-throughput)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 47}, "id": 21, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_delivered_consume_auto_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages delivered auto ack / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of message acknowledgements coming from consumers that use manual acknowledgement mode.\n\n* [Consumer Acknowledgements](https://www.rabbitmq.com/confirms.html)\n* [Consumer Prefetch](https://www.rabbitmq.com/consumer-prefetch.html)\n* [Consumer Acknowledgement Modes, Prefetch and Throughput](https://www.rabbitmq.com/confirms.html#channel-qos-prefetch-throughput)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 52}, "id": 22, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_acknowledged_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Messages acknowledged / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages delivered to polling consumers that use automatic acknowledgement mode.\n\nThe use of polling consumers is highly inefficient and therefore strongly discouraged.\n\n* [Fetching individual messages](https://www.rabbitmq.com/consumers.html#fetching)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 0}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/rabbit/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 52}, "id": 24, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_delivered_get_auto_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Polling operations with auto ack / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of polling consumer operations that yield no result.\n\nAny value above zero means that RabbitMQ resources are wasted by polling consumers.\n\nCompare this metric to the other polling consumer metrics to see the inefficiency rate.\n\nThe use of polling consumers is highly inefficient and therefore strongly discouraged.\n\n* [Fetching individual messages](https://www.rabbitmq.com/consumers.html#fetching)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 0}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/rabbit/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 57}, "id": 25, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_get_empty_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Polling operations that yield no result / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of messages delivered to polling consumers that use manual acknowledgement mode.\n\nThe use of polling consumers is highly inefficient and therefore strongly discouraged.\n\n* [Fetching individual messages](https://www.rabbitmq.com/consumers.html#fetching)\n* [Consumers](https://www.rabbitmq.com/consumers.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 0}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/rabbit/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 57}, "id": 23, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_global_messages_delivered_get_manual_ack_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Polling operations with manual ack / s", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 62}, "id": 53, "panels": [], "title": "QUEUES", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Total number of queue masters  per node. \n\nThis metric makes it easy to see sub-optimal queue distribution in a cluster.\n\n* [Queue Masters, Data Locality](https://www.rabbitmq.com/ha.html#master-migration-data-locality)\n* [Queues](https://www.rabbitmq.com/queues.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 63}, "id": 57, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rabbitmq_queues * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Total queues", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of queue declarations performed by clients.\n\nLow sustained values above zero are to be expected. High rates may be indicative of queue churn or high rates of connection recovery. Confirm connection recovery rates by using the _Connections opened_ metric.\n\n* [Queues](https://www.rabbitmq.com/queues.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 4, "x": 12, "y": 63}, "id": 58, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_queues_declared_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Queues declared / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of new queues created (as opposed to redeclarations).\n\nLow sustained values above zero are to be expected. High rates may be indicative of queue churn or high rates of connection recovery. Confirm connection recovery rates by using the _Connections opened_ metric.\n\n* [Queues](https://www.rabbitmq.com/queues.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 4, "x": 16, "y": 63}, "id": 60, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_queues_created_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Queues created / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of queues deleted.\n\nLow sustained values above zero are to be expected. High rates may be indicative of queue churn or high rates of connection recovery. Confirm connection recovery rates by using the _Connections opened_ metric.\n\n* [Queues](https://www.rabbitmq.com/queues.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 63}, "id": 59, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_queues_deleted_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Queues deleted / s", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 68}, "id": 51, "panels": [], "title": "CHANNELS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Total number of channels on all currently opened connections.\n\nIf this metric grows monotonically it is highly likely a channel leak in one of the applications. Confirm channel leaks by using the _Channels opened_ and _Channels closed_ metrics.\n\n* [Channel Leak](https://www.rabbitmq.com/channels.html#channel-leaks)\n* [Channels](https://www.rabbitmq.com/channels.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 69}, "id": 54, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rabbitmq_channels * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Total channels", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of new channels opened by applications across all connections. Channels are expected to be long-lived.\n\nLow sustained values above zero are to be expected. High rates may be indicative of channel churn or mass connection recovery. Confirm connection recovery rates by using the _Connections opened_ metric.\n\n* [High Channel Churn](https://www.rabbitmq.com/channels.html#high-channel-churn)\n* [Channels](https://www.rabbitmq.com/channels.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 69}, "id": 55, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_channels_opened_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Channels opened / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of channels closed by applications across all connections. Channels are expected to be long-lived.\n\nLow sustained values above zero are to be expected. High rates may be indicative of channel churn or mass connection recovery. Confirm connection recovery rates by using the _Connections opened_ metric.\n\n* [High Channel Churn](https://www.rabbitmq.com/channels.html#high-channel-churn)\n* [Channels](https://www.rabbitmq.com/channels.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 69}, "id": 56, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_channels_closed_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Channels closed / s", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 46, "panels": [], "title": "CONNECTIONS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "Total number of client connections.\n\nIf this metric grows monotonically it is highly likely a connection leak in one of the applications. Confirm connection leaks by using the _Connections opened_ and _Connections closed_ metrics.\n\n* [Connection Leak](https://www.rabbitmq.com/connections.html#monitoring)\n* [Connections](https://www.rabbitmq.com/connections.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 75}, "id": 47, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rabbitmq_connections * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Total connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of new connections opened by clients. Connections are expected to be long-lived.\n\nLow sustained values above zero are to be expected. High rates may be indicative of connection churn or mass connection recovery.\n\n* [Connection Leak](https://www.rabbitmq.com/connections.html#monitoring)\n* [Connections](https://www.rabbitmq.com/connections.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 75}, "id": 48, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_connections_opened_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Connections opened / s", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The rate of connections closed. Connections are expected to be long-lived.\n\nLow sustained values above zero are to be expected. High rates may be indicative of connection churn or mass connection recovery.\n\n* [Connections](https://www.rabbitmq.com/connections.html)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?0(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#56A64B", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?1(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2CC0C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?2(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#3274D9", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?3(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#A352CC", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?4(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF780A", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?5(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#96D98D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?6(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFEE52", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?7(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#8AB8FF", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?8(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CA95E5", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/^rabbit@[a-zA-Z\\.\\-]*?9(\\b|\\.)/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FFB357", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 75}, "id": 49, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi"}}, "pluginVersion": "8.3.4", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(rate(rabbitmq_connections_closed_total[60s]) * on(instance, job) group_left(rabbitmq_cluster, rabbitmq_node) rabbitmq_identity_info{rabbitmq_cluster=\"$rabbitmq_cluster\", namespace=\"$namespace\"}) by(rabbitmq_node)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{rabbitmq_node}}", "refId": "A"}], "title": "Connections closed / s", "type": "timeseries"}], "preload": false, "refresh": "15s", "schemaVersion": 40, "tags": ["rabbitmq-prometheus", "infra"], "templating": {"list": [{"current": {"text": "default", "value": "default"}, "hide": 2, "includeAll": false, "label": "datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {"text": "infra", "value": "infra"}, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "definition": "label_values(rabbitmq_identity_info, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(rabbitmq_identity_info, namespace)", "refId": "Prometheus-namespace-Variable-Query"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "<EMAIL>", "value": "<EMAIL>"}, "datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "definition": "label_values(rabbitmq_identity_info{namespace=\"$namespace\"}, rabbitmq_cluster)", "includeAll": false, "label": "RabbitMQ Cluster", "name": "rabbitmq_cluster", "options": [], "query": {"query": "label_values(rabbitmq_identity_info{namespace=\"$namespace\"}, rabbitmq_cluster)", "refId": "Prometheus-rabbitmq_cluster-Variable-Query"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["15s", "30s", "1m", "5m", "10m"]}, "timezone": "", "title": "RabbitMQ-Overview", "uid": "Kn5xm-gZk", "version": 1, "weekStart": ""}