{{- define "argocd-apps.project" }}
  {{- $projectName := .name -}}
  {{- $projectData := .data -}}
  {{- if $projectData.enabled -}}
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  {{- with $projectData.additionalAnnotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  {{- with $projectData.additionalLabels }}
  labels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: {{ $projectName }}
  {{- with $projectData.namespace }}
  namespace: {{ . }}
  {{- end }}
  {{- with $projectData.finalizers }}
  finalizers:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- with $projectData.permitOnlyProjectScopedClusters }}
  permitOnlyProjectScopedClusters: {{ . }}
  {{- end }}
  {{- with $projectData.description }}
  description: {{ . }}
  {{- end }}
  {{- with $projectData.sourceRepos }}
  sourceRepos:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.destinations }}
  destinations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.clusterResourceWhitelist }}
  clusterResourceWhitelist:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.clusterResourceBlacklist }}
  clusterResourceBlacklist:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.namespaceResourceBlacklist }}
  namespaceResourceBlacklist:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.namespaceResourceWhitelist }}
  namespaceResourceWhitelist:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.orphanedResources }}
  orphanedResources:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.roles }}
  roles:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.syncWindows }}
  syncWindows:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.signatureKeys }}
  signatureKeys:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with $projectData.sourceNamespaces }}
  sourceNamespaces:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
{{- end }}
