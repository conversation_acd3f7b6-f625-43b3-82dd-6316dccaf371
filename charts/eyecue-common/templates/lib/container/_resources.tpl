{{- define "eyecue.common.lib.container.resources" -}}
  {{- $rootContext := .rootContext -}}
  {{- $resourcesObject := .resourcesObject | default dict -}}
  {{- /* Default resources for the container */ -}}
  {{- $defaultResources := dict "limits" (dict "cpu" "1" "memory" "1Gi") "requests" (dict "cpu" "500m" "memory" "500Mi") -}}
  {{- $resources := merge $resourcesObject $defaultResources -}}
  {{- include "common.tplvalues.render" (dict "value" $resources "context" $rootContext) | nindent 2 -}}
{{- end -}}
