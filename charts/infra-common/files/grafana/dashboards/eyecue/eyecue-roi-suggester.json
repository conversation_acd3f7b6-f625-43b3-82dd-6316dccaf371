{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 14, "links": [], "liveNow": false, "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [{"datasource": {}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "roi_suggestor_jobs_process_memory_usage", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "ROI Suggestor Jobs memory usage", "type": "timeseries"}, {"datasource": {}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "roi_suggestor_assembler_process_memory_usage", "instant": true, "key": "Q-00135311-dc2c-48b6-bff7-05f119ca385c-0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Roi suggestor assembler memory usage", "transformations": [], "type": "timeseries"}], "title": "Memory usage", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 13, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "custom": {"fillOpacity": 70, "lineWidth": 1}, "mappings": [], "max": 1, "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 2}, "id": 8, "options": {"colWidth": 0.8, "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "rowHeight": 0.9, "showValue": "never", "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "builder", "exemplar": false, "expr": "max_over_time(roi_suggestor_assembler_generated_image_timestamp{heatmap_type=\"$aggregate_type\"}[1h])", "format": "time_series", "instant": false, "interval": "1h", "legendFormat": "camera-{{camera_id}}", "range": true, "refId": "A"}], "title": "PNG Aggregations", "type": "status-history"}], "title": "$aggregate_type status", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 14, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "roi_suggestor_jobs_accumulated_heatmap_detections", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Panel Title", "transformations": [{"id": "calculateField", "options": {}}], "type": "timeseries"}, {"datasource": {}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "roi_suggestor_jobs_accumulated_json_detections", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Accumulated raw detections", "type": "timeseries"}], "title": "Raw detections", "type": "row"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["eyecue"], "templating": {"list": [{"allValue": "All heatmaps", "current": {"selected": false, "text": "heatmap", "value": "heatmap"}, "definition": "label_values(heatmap_type)", "hide": 0, "includeAll": false, "multi": false, "name": "aggregate_type", "options": [], "query": {"query": "label_values(heatmap_type)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "011", "value": "011"}, "definition": "label_values(camera_id)", "hide": 0, "includeAll": false, "multi": false, "name": "camera_id", "options": [], "query": {"query": "label_values(camera_id)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ROI Suggestor", "version": 1, "weekStart": ""}