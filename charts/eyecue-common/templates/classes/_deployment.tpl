{{- define "eyecue.common.deployment" -}}
  {{- $rootContext := .rootContext -}}
  {{- $deploymentObject := .object -}}
  {{- $labels := merge
    (dict "app.kubernetes.io/name" $deploymentObject.name)
    ($deploymentObject.labels | default dict)
    (include "eyecue.common.lib.metadata.allLabels" $rootContext | fromYaml)
  -}}

  {{- $annotations := $deploymentObject.annotations | default dict -}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $deploymentObject.name }}
  namespace: {{ include "eyecue.common.namespace" $rootContext }}
  {{- with $labels }}
  labels:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- with $annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  replicas: {{ $deploymentObject.replicas | default 1 }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ $deploymentObject.name }}
      {{- include "eyecue.common.lib.metadata.selectorLabels" $rootContext | nindent 6 }}
  template:
    metadata:
      labels:
        {{- with $labels }}
          {{- range $key, $value := . }}
          {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 8 }}
          {{- end }}
        {{- end }}
      {{- with $annotations }}
      annotations:
        {{- range $key, $value := . }}
        {{- printf "%s: %s" $key (tpl $value $rootContext | toYaml ) | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      {{- with $deploymentObject.volumes }}
      volumes:
        {{ include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 8 | trim }}
      {{- end }}

      {{- with $deploymentObject.imagePullSecrets }}
      imagePullSecrets:
        {{ include "common.tplvalues.render" (dict "value" . "context" $rootContext) | nindent 8 | trim }}
      {{- end }}

      {{- with $deploymentObject.initContainers }}
      initContainers:
        {{- range $initContainer := . }}
        - {{ include "eyecue.common.lib.container.spec" (dict "rootContext" $rootContext "containerObject" $initContainer) | nindent 10 | trim }}
        {{- end }}
      {{- end }}

      {{- with $deploymentObject.containers }}
      containers:
        {{- range $container := . }}
        - {{ include "eyecue.common.lib.container.spec" (dict "rootContext" $rootContext "containerObject" $container) | nindent 10 | trim }}
        {{- end }}
      {{- end }}
{{- end -}}
