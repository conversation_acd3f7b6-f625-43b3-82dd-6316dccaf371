{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "iteration": 1662651374215, "links": [], "liveNow": false, "panels": [{"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolate<PERSON>ur<PERSON>", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "timeseries", "datasource": null, "description": "", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 2, "legend": {"show": false}, "pluginVersion": "8.4.4", "reverseYBuckets": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": true, "expr": "sum(increase(eyecue_server_reid_time_seconds_bucket{roi_id=\"$roi_id\"}[30m])) by (le)", "format": "time_series", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Reidentification time", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": null, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.2.6", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "exemplar": false, "expr": "eyecue_server_reid_time_seconds_bucket{le=\"10.0\",roi_id=\"$roi_id\"} / ignoring (le) eyecue_server_reid_time_seconds_count", "format": "time_series", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "<PERSON> longer than 10s", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.2.6", "targets": [{"exemplar": true, "expr": "eyecue_server_lost_assets_count_total{roi_id=\"$roi_id\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Lost assets count", "type": "stat"}], "refresh": false, "schemaVersion": 32, "style": "dark", "tags": ["eyecue"], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": "D", "value": "D"}, "datasource": null, "definition": "label_values(roi_id)", "description": "", "error": null, "hide": 0, "includeAll": false, "label": "roi_id", "multi": false, "name": "roi_id", "options": [], "query": {"query": "label_values(roi_id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Server dashboard", "uid": "V_s8BbWVz", "version": 1}