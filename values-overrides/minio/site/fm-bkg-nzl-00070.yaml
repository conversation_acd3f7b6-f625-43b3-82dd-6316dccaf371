# https://github.com/bitnami/charts/blob/minio/16.0.10/bitnami/minio/values.yaml
provisioning:
  buckets:
    - name: best-shots
      versioning: Suspended
      lifecycle:
        - id: BestShotsLifecycle
          expiry:
            days: 7
    - name: eyecue-images
      versioning: Suspended
      lifecycle:
        - id: ImagesLifecycle
          expiry:
            days: 7
    - name: eyecue-mosaic
      versioning: Suspended
      lifecycle:
        - id: MosaicLifecycle
          # Shorter expiry for mosaic as this server only has
          # one hard disk resulting in it filling up quickly.
          expiry:
            days: 2
    - name: validations
      versioning: Suspended
      lifecycle:
        - id: ValidationsLifecycle
          expiry:
            days: 7
