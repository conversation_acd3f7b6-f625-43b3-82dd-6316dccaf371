{"type": "object", "additionalProperties": false, "properties": {"annotations": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "labels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "name": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "binaryData": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["name"], "oneOf": [{"required": ["data"]}, {"required": ["binaryData"]}]}