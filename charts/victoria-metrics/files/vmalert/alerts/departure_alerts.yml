groups:
  - name: departure-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
      - alert: EyecueDepartureAnomalyAlert
        expr: departures_mongodb_zscore > 3.5
        labels:
          severity: critical
        annotations:
          summary: "Anomaly in the departure value on {{ $labels.siteId }} for the last hour"
          description: |
            The value of departures is above the threshold for the anomaly score on {{ $labels.siteId }} in the previous hour.

            More context on the error: https://www.notion.so/fingermark/EyecueDepartureAnomalyAlert-acdf042a1f324c518b5df5849e0b8c1d

      - alert: EyecueDepartureEventAlert
        expr: |
          sum(changes(departures_mongodb_received_departure_messages_counter_total{}[60m])) < 1
        labels:
          severity: critical
        annotations:
          summary: "Departures MongoDB on {{ $labels.siteId }} has not received departure event from tracker for over 1 hour"
          description: |
            The Departures MongoDB on {{ $labels.siteId }} has not received departure event from tracker for over 1 hour.
      - alert: EyecueArrivalEventAlert
        expr: |
          sum(changes(departures_mongodb_received_arrival_messages_counter_total{}[60m])) < 1
        labels:
          severity: critical
        annotations:
          summary: "Departures MongoDB on {{ $labels.siteId }} has not received arrival event from tracker for over 1 hour"
          description: |
            The Departures MongoDB on {{ $labels.siteId }} has not received arrival event from tracker for over 1 hour.
      - alert: EyecueDeparturStatsNotSentAlert
        expr: |
          absent_over_time(departures_mongodb_metrics_sent_total[5m]) == 1
        keep_firing_for: 30m
        labels:
          severity: critical
        annotations:
          summary: "Departures MongoDB on {{ $labels.siteId }} has not sent departure stats for over 5 mins"
          description: |
            The Departures MongoDB on {{ $labels.siteId }} has not sent departure stats for over 5 mins.
