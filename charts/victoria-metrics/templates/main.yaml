{{- range $persistentVolume := .Values.persistentVolumes }}
{{ include "eyecue.common.pv" (dict "rootContext" $ "object" $persistentVolume) -}}
{{- end -}}

{{- range $persistentVolumeClaim := .Values.persistentVolumeClaims }}
{{ include "eyecue.common.pvc" (dict "rootContext" $ "object" $persistentVolumeClaim) -}}
{{- end -}}

{{- range $configMap := .Values.configMaps }}
{{ include "eyecue.common.configMap" (dict "rootContext" $ "object" $configMap) -}}
{{- end -}}

{{- if and .Values.victoriaMetricsAlert.enabled .Values.victoriaMetricsAlert.server.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.victoriaMetricsAlert.server.configMap }}
  namespace: {{ include "eyecue.common.namespace" . }}
data:
{{ (.Files.Glob "files/vmalert/alerts/*").AsConfig | indent 2 }}

{{- end }}
