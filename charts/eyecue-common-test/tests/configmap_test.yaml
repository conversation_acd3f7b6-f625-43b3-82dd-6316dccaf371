---
# yaml-language-server: $schema=https://raw.githubusercontent.com/helm-unittest/helm-unittest/main/schema/helm-testsuite.json
suite: Configmap Test Suite
templates:
  - classes/configmap.yaml
tests:
  - it: Should create a simple configmap
    set:
      name: testing
      data:
        key1: value1
        key2: value2
    asserts:
      - isKind:
          of: ConfigMap
      - equal:
          path: metadata
          value:
            name: testing
            namespace: NAMESPACE
      - equal:
          path: data
          value:
            key1: value1
            key2: value2
