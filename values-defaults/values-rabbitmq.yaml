# yaml-language-server: https://raw.githubusercontent.com/bitnami/charts/refs/tags/rabbitmq/15.0.3/bitnami/rabbitmq/values.schema.json
# https://github.com/bitnami/charts/blob/rabbitmq/15.0.3/bitnami/rabbitmq/values.yaml
pdb:
  create: false

metrics:
  enabled: true

extraPlugins: "rabbitmq_mqtt rabbitmq_web_mqtt"

resources:
  requests:
    cpu: 300m
    memory: 300Mi
  limits:
    memory: 512Mi

volumePermissions:
  enabled: true

persistence:
  enabled: true
  existingClaim: rabbitmq-pvc

auth:
  password: q1w2e3r4
  erlangCookie: eyecue

  tls:
    enabled: true
    existingSecret: eyeq-self-sign-ssl-credentials
    failIfNoPeerCert: false

extraConfiguration: |
  web_mqtt.ssl.port       = 15676
  web_mqtt.ssl.cacertfile = /opt/bitnami/rabbitmq/certs/ca_certificate.pem
  web_mqtt.ssl.certfile   = /opt/bitnami/rabbitmq/certs/server_certificate.pem
  web_mqtt.ssl.keyfile    = /opt/bitnami/rabbitmq/certs/server_key.pem

loadDefinition:
  enabled: true
  existingSecret: eyecue-rabbitmq-definition-secret

clustering:
  enabled: false

extraContainerPorts:
  - name: mqtt
    containerPort: 1883
  - name: mqtt-tls
    containerPort: 8883
  - name: web-mqtt
    containerPort: 15675
  - name: web-mqtt-tls
    containerPort: 15676

networkPolicy:
  extraIngress:
    - ports:
      - port: 1883
        protocol: TCP
      - port: 8883
        protocol: TCP
      - port: 15675
        protocol: TCP
      - port: 15676
        protocol: TCP

service:
  extraPorts:
    - name: mqtt
      port: 1883
      targetPort: mqtt
    - name: mqtt-tls
      port: 8883
      targetPort: mqtt-tls
    - name: web-mqtt
      port: 15675
      targetPort: web-mqtt
    - name: web-mqtt-tls
      port: 15676
      targetPort: web-mqtt-tls

  extraPortsHeadless:
    - name: mqtt
      port: 1883
      targetPort: 1883
    - name: mqtt-tls
      port: 8883
      targetPort: 8883
    - name: web-mqtt
      port: 15675
      targetPort: 15675
    - name: web-mqtt-tls
      port: 15676
      targetPort: 15676

extraDeploy:
  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: rabbitmq-pvc
    spec:
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: 1Gi
      storageClassName: ""
      volumeName: rabbitmq-pv
  - kind: PersistentVolume
    apiVersion: v1
    metadata:
      name: rabbitmq-pv
    spec:
      capacity:
        storage: 1Gi
      accessModes:
        - ReadWriteOnce
      storageClassName: ""
      volumeMode: Filesystem
      persistentVolumeReclaimPolicy: Retain
      hostPath:
        path: /media/fingermark/storage/rabbitmq-infra
  - apiVersion: v1
    kind: Service
    metadata:
      name: eyecue-rabbitmq-external
      labels:
        app.kubernetes.io/service: eyecue-rabbitmq-external
    spec:
      type: NodePort
      ports:
        - port: 15672
          targetPort: 15672
          protocol: TCP
          name: management
          nodePort: 30672
        - port: 15675
          targetPort: 15675
          protocol: TCP
          name: web-mqtt
          nodePort: 30675
        - port: 15676
          targetPort: 15676
          protocol: TCP
          name: web-mqtt-tls
          nodePort: 30676
      selector:
        app.kubernetes.io/instance: infra
        app.kubernetes.io/name: rabbitmq
  - apiVersion: v1
    kind: Secret
    metadata:
      name: eyecue-rabbitmq-definition-secret
    stringData:
      load_definition.json: |
        {
          "users": [
            {
              "name": "{{ .Values.siteId }}",
              "password": "{{ .Values.global.configuration.siteId }}",
              "tags": "management"
            },
            {
              "name": "services",
              "password_hash": "83kurbIX0d4en9kQ1Q4X0WmiE1cWyOxA0SK++TV8z8MxT3dJ",
              "hashing_algorithm": "rabbit_password_hashing_sha256",
              "tags": "management"
            },
            {
              "name": "dashboard",
              "password_hash": "QO5hC9xpnfatjjIzQCF8Ii5gHzGwW2g1vdOR1qUIUHfVn6EF",
              "hashing_algorithm": "rabbit_password_hashing_sha256",
              "tags": "impersonator"
            }
          ],
          "vhosts": [
            {
              "name": "/"
            }
          ],
          "permissions": [
            {
              "user": "{{ .Values.siteId }}",
              "vhost": "/",
              "configure": ".*",
              "write": ".*",
              "read": ".*"
            },
            {
              "user": "services",
              "vhost": "/",
              "configure": ".*",
              "write": ".*",
              "read": ".*"
            },
            {
              "user": "dashboard",
              "vhost": "/",
              "configure": ".*",
              "write": ".*",
              "read": ".*"
            }
          ],
          "topic_permissions": [
            {
              "user": "{{ .Values.siteId }}",
              "vhost": "/",
              "exchange": "",
              "write": ".*",
              "read": ".*"
            },
            {
              "user": "services",
              "vhost": "/",
              "exchange": "",
              "write": ".*",
              "read": ".*"
            },
            {
              "user": "dashboard",
              "vhost": "/",
              "exchange": "",
              "write": ".*",
              "read": ".*"
            }
          ],
          "parameters": [],
          "policies": [
            {
              "vhost": "/",
              "name": "q-limit",
              "pattern": ".*",
              "apply-to": "queues",
              "definition": {
                "max-length": 10000,
                "message-ttl": 600000
              },
              "priority": 0
            }
          ],
          "queues": [],
          "exchanges": [],
          "bindings": []
        }
