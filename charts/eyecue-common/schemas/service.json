{"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["ClusterIP", "LoadBalancer", "ExternalName", "NodePort"], "default": "ClusterIP"}, "annotations": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "labels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}, "clusterIP": {"type": "string"}, "loadBalancerIP": {"type": "string"}, "loadBalancerSourceRanges": {"type": "array", "items": {"type": "string"}}, "loadBalancerClass": {"type": "string"}, "externalName": {"type": "string"}, "internalTrafficPolicy": {"type": "string", "enum": ["Cluster", "Local"]}, "externalTrafficPolicy": {"type": "string", "enum": ["Cluster", "Local"]}, "allocateLoadBalancerNodePorts": {"type": "boolean"}, "sessionAffinity": {"type": "string", "enum": ["None", "ClientIP"]}, "sessionAffinityConfig": {"type": "object", "properties": {"clientIP": {"type": "object", "properties": {"timeoutSeconds": {"type": "integer"}}}}}, "externalIPs": {"type": "array", "items": {"type": "string"}}, "publishNotReadyAddresses": {"type": "boolean"}, "ipFamilyPolicy": {"type": "string", "enum": ["SingleStack", "PreferDualStack", "RequireDualStack"]}, "ipFamilies": {"type": "array", "items": {"type": "string", "enum": ["IPv4", "IPv6"]}}, "ports": {"type": "object", "additionalProperties": {"type": "object", "properties": {"port": {"type": "integer"}, "targetPort": {"type": ["integer", "string"]}, "protocol": {"type": "string", "enum": ["TCP", "UDP", "HTTP", "HTTPS"]}, "nodePort": {"type": "integer"}, "appProtocol": {"type": "string"}}, "required": ["port"]}}, "extraSelectorLabels": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["name", "ports"]}