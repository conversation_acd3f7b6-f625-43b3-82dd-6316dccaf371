# Created by https://www.toptal.com/developers/gitignore/api/helm,linux,macos
# Edit at https://www.toptal.com/developers/gitignore?templates=helm,linux,macos

### Helm ###
# Chart dependencies
**/charts/*.tgz

### Linux ###
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon


# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

### macOS Patch ###
# iCloud generated files
*.icloud

# End of https://www.toptal.com/developers/gitignore/api/helm,linux,macos

.env
build/
# Ignore the Chart.lock file so that ArgoCD doesn't
# require us to manually add the helm repos in s3
charts/*/Chart.lock
!charts/common/Chart.lock
!charts/common-test/Chart.lock
