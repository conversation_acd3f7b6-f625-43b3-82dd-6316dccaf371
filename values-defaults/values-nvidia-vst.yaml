namespace: infra

securityContext:
  runAsUser: 0
  runAsGroup: 0
  runAsNonRoot: false

imagePullSecrets:
  - name: ngc-docker-reg-secret

applicationSpecs:
  vms:
    containers:
      vms-container:
        image:
          repository: nvcr.io/nvidia/ace/vst
          tag: 1.3.0-25.01.4-x86_64

    services:
      svc:
        ports:
        - name: vms
          nodePort: 30000
          port: 30000
          protocol: TCP
        - name: rtsp
          nodePort: 30554
          port: 30554
          protocol: TCP
        - name: vms-grpc
          port: 50051
          protocol: TCP
        - name: rtsp-in
          nodePort: 30200
          port: 30200
          protocol: UDP
          range: 30

storageClaims:
  local-storage:
    annotations:
      helm.sh/resource-policy: keep
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 100Gi
      storageClassName: ""

configs:
  rtsp_streams.json:
    streams: []
  vst_config.json:
    data:
      always_recording: true
    debug:
      enable_highlighting_logs: true
    notifications:
      enable_notification: false
    onvif:
      default_framerate: 6
      device_discovery_timeout_secs: 60
      onvif_request_timeout_secs: 60
    network:
      static_turnurl_list:
        - 000000002067975440:60Sgs5TouO6lkgXHS/GplmiIEFs=@relay1.expressturn.com:3480
  vst_storage.json:
    total_video_storage_size_MB: 100000
  adaptor_config.json:
    vst:
      - control_adaptor_lib_path: prebuilts/x86_64/onvif_client.so
        control_sensor_lib_path:
        - lib_path: prebuilts/x86_64/libamcrest_controller.so
          manufacturer: Amcrest
        discovery_adaptor_lib_path: prebuilts/x86_64/onvif_discovery.so
        enabled: false
        id: 044bc643-33c5-479a-b988-10d0bbc4e05c
        name: onvif
        need_recording: true
        need_rtsp_server: true
        type: vst
      - enabled: true
        control_adaptor_lib_path: prebuilts/x86_64/rtsp_streams.so
        id: 6cdec7d7-0f30-450c-a78a-756c3e132fd3
        name: vst_rtsp
        need_recording: true
        need_rtsp_server: true
        type: vst
