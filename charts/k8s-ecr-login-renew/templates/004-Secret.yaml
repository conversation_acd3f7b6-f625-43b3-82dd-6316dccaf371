{{- if .Values.awsAccessKeyId }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ required "A secret name must be defined" .Values.aws.secretName }}
  namespace: {{ .Release.Namespace | default "default" }}
{{- if .Values.forHelm }}
  labels:
    app.kubernetes.io/name: {{ .Chart.Name }}
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}
type: Opaque
stringData:
  {{ required "Secret key for access key id must be defined" .Values.aws.secretKeys.accessKeyId }}: {{ required "Value for access key id must be defined" .Values.awsAccessKeyId }}
  {{ required "Secret key for secret access key must be defined" .Values.aws.secretKeys.secretAccessKey }}: {{ required "Value for secret access key must be defined" .Values.awsSecretAccessKey }}
{{- end }}
