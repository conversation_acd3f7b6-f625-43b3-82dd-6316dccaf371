groups:
  - name: mosaic-alerts
    concurrency: 1
    labels:
      siteId: "%{SITE_ID}"
    rules:
      - alert: FrameRecordingTookMoreThan1Sec
        expr: mosaic_recorder_frame_recording_time > 1000
        labels:
          severity: warning
        annotations:
          summary: "Mosaic frame took more than 1 sec to generate."
          description: |
            Mosaic frame took more than 1 sec to generate on server {{ $labels.siteId }}.
            For optimal operation, mosaic frames should be generated under 1000ms, this alert makes us aware of which sites are missing this deadline.

            More context on the error: https://www.notion.so/fingermark/FrameRecordingTookMoreThan1Sec-e6dd30ffe38a4eaab62dd821a138d86e

      - alert: MosaicVideoUnder100MB
        expr: mosaic_assembler_mosaic_video_file_size < 104857600
        labels:
          severity: warning
        annotations:
          summary: "Mosaic video has less than 100 MB"
          description: |
            Server {{ $labels.siteId }} generated a mosaic video under 100 MB

            More context on the error: https://www.notion.so/fingermark/MosaicVideoUnder100MB-130366aa10d54349be735c8d443d69e1

      - alert: MosaicFramesMissingMinio
        expr: absent_over_time(mosaic_recorder_frame_upload_to_minio[15m]) == 1
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Mosaic is not send frames to minio"
          description: |
            Server {{ $labels.siteId }} did not send frames to minio for more than 30m

            More context on the error: https://www.notion.so/fingermark/MosaicFramesMissingMinio-1ddfec64ab354173a03ae9c766dc3ac8

      - alert: MosaicLostFramesMinio
        expr: increase(mosaic_recorder_frame_upload_to_minio[1h]) / 3600 < 0.9
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Mosaic lost quite a bit of frames last hour"
          description: |
            Server {{ $labels.siteId }} lost more than 10% of frames last hour.

            More context on the error: https://www.notion.so/fingermark/MosaicFramesMissingMinio-1ddfec64ab354173a03ae9c766dc3ac8

      - alert: MosaicLostFramesMinio
        expr: increase(mosaic_recorder_frame_upload_to_minio[1h]) / 3600 < 0.7
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: "Mosaic lost quite a lot of frames last hour"
          description: |
            Server {{ $labels.siteId }} lost more than 30% of frames last hour.

            More context on the error: https://www.notion.so/fingermark/MosaicFramesMissingMinio-1ddfec64ab354173a03ae9c766dc3ac8

      - alert: MosaicFramesMissingMinio
        expr: absent_over_time(mosaic_recorder_frame_upload_to_minio[15m]) == 1
        for: 2h
        labels:
          severity: critical
        annotations:
          summary: "Mosaic is not send frames to minio"
          description: |
            Server {{ $labels.siteId }} did not send frames to minio for more than 2h

            More context on the error: https://www.notion.so/fingermark/MosaicFramesMissingMinio-1ddfec64ab354173a03ae9c766dc3ac8
