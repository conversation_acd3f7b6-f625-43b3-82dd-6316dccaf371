{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4, "links": [], "liveNow": false, "panels": [{"description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "id": 30, "options": {"calculate": false, "cellGap": 1, "color": {"exponent": 0.5, "fill": "dark-orange", "max": 60, "min": 0, "mode": "scheme", "reverse": true, "scale": "exponential", "scheme": "RdYlGn", "steps": 64}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "hidden", "reverse": false}}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "increase(mosaic_recorder_frame_upload_to_minio[1m])", "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Frames upload to minio", "type": "heatmap"}, {"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 5, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 3}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 14, "x": 0, "y": 4}, "id": 19, "interval": "1m", "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "sum by (status) (sum_over_time(mosaic_recorder_recorder_stopped[1d]))", "hide": false, "legendFormat": "{{status}}", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum by (camera_id) (mosaic_recorder_empty_frame_count_total)", "hide": false, "legendFormat": "empty frames camera-{{camera_id}}", "range": true, "refId": "A"}], "timeFrom": "1d", "title": "Failures", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 14, "y": 4}, "hideTimeOverride": true, "id": 28, "interval": "1m", "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "sum(count_over_time(mosaic_recorder_recorder_started[1h])) - sum(count_over_time(mosaic_recorder_recorder_stopped[1h]))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "timeFrom": "1h", "title": "Crashes", "type": "stat"}, {"gridPos": {"h": 8, "w": 8, "x": 16, "y": 4}, "id": 22, "options": {"alertInstanceLabelFilter": "", "alertName": "", "dashboardAlerts": false, "groupBy": [], "groupMode": "default", "maxItems": 20, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": false, "pending": true}, "viewMode": "list"}, "title": "Firing alerts", "type": "alertlist"}, {"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 1000000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 12}, "hideTimeOverride": true, "id": 31, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["max"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "mosaic_recorder_get_images_time", "hide": false, "legendFormat": "Fetch frames from Redis", "range": true, "refId": "F"}, {"editorMode": "code", "expr": "mosaic_recorder_get_mosaic_time", "hide": false, "legendFormat": "Build mosaic frame", "range": true, "refId": "G"}, {"editorMode": "code", "expr": "mosaic_recorder_upload_image_time\n\n", "legendFormat": "MInio frame upload time", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "mosaic_recorder_dashboard_screenshot_time", "hide": false, "legendFormat": "Dashboard screenshot time", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "mosaic_recorder_frame_recording_time * 1000 ", "hide": false, "legendFormat": "Whole frame recording time", "range": true, "refId": "C"}], "title": "Max timings", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 1000000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 12}, "hideTimeOverride": true, "id": 33, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "mosaic_recorder_get_images_time", "hide": false, "legendFormat": "Fetch frames from Redis", "range": true, "refId": "F"}, {"editorMode": "code", "expr": "mosaic_recorder_get_mosaic_time", "hide": false, "legendFormat": "Build mosaic frame", "range": true, "refId": "G"}, {"editorMode": "code", "expr": "mosaic_recorder_upload_image_time\n\n", "legendFormat": "MInio frame upload time", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "mosaic_recorder_dashboard_screenshot_time", "hide": false, "legendFormat": "Dashboard screenshot time", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "mosaic_recorder_frame_recording_time * 1000 ", "hide": false, "legendFormat": "Whole frame recording time", "range": true, "refId": "C"}], "title": "Avg timings", "type": "stat"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 17, "panels": [{"description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 25}, "id": 14, "options": {"calculate": false, "cellGap": 1, "color": {"exponent": 0.5, "fill": "dark-orange", "max": 60, "min": 0, "mode": "scheme", "reverse": true, "scale": "exponential", "scheme": "RdYlGn", "steps": 64}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false}}, "pluginVersion": "9.5.2", "targets": [{"editorMode": "code", "expr": "sum by(camera_id) (increase(mosaic_recorder_processed_frame_count_total[1m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Frames per minute per camera", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "How long each step takes to run when creating the mosaic frame, it can show if the upstream services are running as expected.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 33}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_dashboard_screenshot_time / 1000", "hide": false, "legendFormat": "Take dashboard screenshot", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_get_images_time / 1000", "hide": false, "legendFormat": "Fetch frames from redis", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_get_reid_time / 1000", "hide": false, "legendFormat": "Generate REID image", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_get_mosaic_time / 1000", "hide": false, "legendFormat": "Generate cameras mosaic", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_combine_mosaic_dashboard_reid_time / 1000", "hide": false, "legendFormat": "Combine dashboard and mosaic", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_add_text_and_bboxes_time / 1000", "hide": false, "legendFormat": "Add text to mosaic", "range": true, "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "expr": "mosaic_recorder_upload_image_time / 1000", "hide": false, "legendFormat": "Upload frame to minio", "range": true, "refId": "G"}], "title": "Mosaic frame creation breakdown", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 41}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "expr": "sum(count_over_time(mosaic_recorder_recorder_started[1h]))", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "editorMode": "code", "expr": "sum(count_over_time(mosaic_recorder_recorder_stopped[1h]))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Panel Title", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 49}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "increase(mosaic_recorder_frame_upload_to_minio[1m])", "interval": "1m", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Frames per minute upload to minio", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 60, "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 49}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "sum by(camera_id) (increase(mosaic_recorder_processed_frame_count_total[1m]))", "legendFormat": "camera{{camera_id}}", "range": true, "refId": "A"}], "title": "Frames per minute per camera", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 57}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "sum by(camera_id) (increase(mosaic_recorder_frame_received_rabbitmq[1m]))", "hide": false, "legendFormat": "camera{{camera_id}}", "range": true, "refId": "A"}], "title": "RabbitMQ messages per camera per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "The framebuffer is a queue of size 10 that accumulates frames to upload to minio. Should stay as close to 0 as possible.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": true, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 10, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 57}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "mosaic_recorder_framebuffer_size", "hide": false, "legendFormat": "Framebuffer size", "range": true, "refId": "A"}], "title": "Framebuffer size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "description": "This panel shows how many empty frames were found per camera, a high number might mean that redis is malfunctioning or mosaic process is being starved.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 65}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "increase(mosaic_recorder_empty_frame_count_total[5m])", "hide": false, "legendFormat": "camera{{camera_id}}", "range": true, "refId": "A"}], "title": "Empty frames", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 65}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_VICTORIAMETRICS}"}, "editorMode": "code", "expr": "rabbitmq_queue_messages_ready{queue=\"mosaic-recorder-tracker-assets\"}", "hide": false, "legendFormat": "Ready messages", "range": true, "refId": "A"}], "title": "Ready messages on RabbitMQ", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 73}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "mosaic_assembler_mosaic_video_file_size", "legendFormat": "__auto", "range": true, "refId": "A"}], "timeFrom": "24h", "title": "Mosaic assembled video size", "type": "timeseries"}], "title": "statistics for nerds", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 3, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 86}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "mosaic_recorder_process_memory_usage", "instant": true, "key": "Q-3accca1c-e9c3-4247-984c-d9704aacda14-0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Mosaic recorder memory usage", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 94}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "builder", "expr": "mosaic_assembler_process_memory_usage", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Mosaic assembler memory usage", "type": "timeseries"}], "title": "Memory", "type": "row"}], "refresh": "1m", "schemaVersion": 38, "style": "dark", "tags": ["eyecue"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Mosaic", "uid": "a71f1144-9344-421d-90b4-53d7ee1b2e2b", "version": 5, "weekStart": ""}