{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Nvidia Triton inference server metrics", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 27, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"dataLinks": [], "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nv_inference_request_success", "legendFormat": "Success {{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "nv_inference_request_failure", "legendFormat": "Failure {{instance}}", "refId": "B"}], "title": "Cumulative Inference Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 7, "options": {"calculate": true, "calculation": {}, "cellGap": 2, "cellValues": {}, "color": {"exponent": 0.5, "fill": "#b4ff00", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "Reds", "steps": 128}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": false}, "rowsFrame": {"layout": "auto"}, "showValue": "never", "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "short"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "sum(increase(nv_inference_load_ratio_bucket[1m])) by (le)", "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>  (Total Time / Compute Time)", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Queue Time (ms)", "axisPlacement": "auto", "axisSoftMax": -1, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"dataLinks": [], "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rate(nv_inference_queue_duration_us[30s]) / 1000", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Queue Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Compute Time", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"dataLinks": [], "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "expr": "rate(nv_inference_compute_duration_us[30s]) / 1000", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Compute Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "${__field.labels[\"model\"]}", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "nv_inference_request_duration_us / 1000", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Inference Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PABDA7AB1AD2A1489"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "${__field.labels[\"node\"]}", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.1", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "nv_gpu_memory_used_bytes / nv_gpu_memory_total_bytes", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "GPU usage", "type": "timeseries"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": ["triton", "infra"], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Triton Inference Server", "uid": "slEY4dsZk", "version": 2, "weekStart": ""}