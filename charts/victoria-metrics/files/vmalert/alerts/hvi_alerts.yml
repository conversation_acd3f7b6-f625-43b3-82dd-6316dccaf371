groups:
  - name: hvi-alerts
    concurrency: 2
    labels:
      siteId: "%{SITE_ID}"
    rules:
    - alert: EyecueHVIUndercountCriticalAlert
      expr: |
        sum(changes(eyecue_tracker_hvi_events_counter_total{}[24h])) - 1.0 *(sum(changes(departures_mongodb_received_departure_messages_counter_total{}[24h]))) < 0
      labels:
        severity: critical
      annotations:
        summary: "The HVI events on {{ $labels.siteId }} might be undercounted"
        description: |
          The HVI events in the past 24 hours were less than the departure rate.

          More context on the error: https://www.notion.so/fingermark/HVI-undercounting-2a74bc2ef3b448eeb56de3a44f34c348
    - alert: EyecueHVIUndercountWarningAlert
      expr: |
        sum(changes(eyecue_tracker_hvi_events_counter_total{}[24h])) - 1.5 *(sum(changes(departures_mongodb_received_departure_messages_counter_total{}[24h]))) < 0
      labels:
        severity: warning
      annotations:
        summary: "The HVI events on {{ $labels.siteId }} might be undercounted"
        description: |
          The HVI events in the past 24 hours were less than 1.5 times the departure rate.

          More context on the error: https://www.notion.so/fingermark/HVI-undercounting-2a74bc2ef3b448eeb56de3a44f34c348
    - alert: EyecueHVIOvercountCriticalAlert
      expr: |
         sum(changes(eyecue_tracker_hvi_events_counter_total{}[24h])) - 3.5 *(sum(changes(departures_mongodb_received_departure_messages_counter_total{}[24h]))) > 0
      labels:
        severity: critical
      annotations:
        summary: "The HVI on {{ $labels.siteId }} might be overcounted."
        description: |
          The HVI events in the past 24 hours were more than 3.5 times the departure rate.

          More context on the error: https://www.notion.so/fingermark/HVI-overcouting-386005bb1bb848058921a51554344436
    - alert: EyecueHVIOvercountWarningAlert
      expr: |
         sum(changes(eyecue_tracker_hvi_events_counter_total{}[24h])) - 2.5 *(sum(changes(departures_mongodb_received_departure_messages_counter_total{}[24h]))) > 0
      labels:
        severity: warning
      annotations:
        summary: "The HVI on {{ $labels.siteId }} might be overcounted."
        description: |
          The HVI events in the past 24 hours were more than 2.5 times the departure rate.

          More context on the error: https://www.notion.so/fingermark/HVI-overcouting-386005bb1bb848058921a51554344436
